const FR = {
  common: {
    login: 'Connexion',
    logout: 'Déconnexion',
    dashboard: 'Tableau de bord',
    profile: 'Profil',
    deliveries: 'Livraisons',
    tracking: 'Suivi',
    reports: 'Rapports',
    duplicate: 'Oui, dupliquer',
    allow: 'Autoriser',
    add: 'Ajouter',
    deny: 'Refuser',
    doItAnyWay: 'Le faire quand même',
    somethingWrong: 'Une erreur s’est produite',
    notUpdatedYet: 'Pas encore mis à jour',
    noMatchesFound: 'Aucune correspondance trouvée',
    usa: 'É.-U. +1',
    can: 'CAN +1',
    ind: 'IND +1',
    errors: {
      noMultipleWhiteSpace: 'Pas d’espaces multiples autorisés',
      noSpacialCharacters: 'Aucun caractère spécial n’est autorisé',
      noWhiteSpace: 'Pas d’espaces autorisés',
      valueMustBeMoreThan1: 'La valeur doit être supérieure à 1',
    },
    error: 'Erreur',
    warn: 'Avertissement',
    success: 'Succ<PERSON>',
    update: 'Mettre à jour',
    leave: 'Quitter',
    stay: 'Rester',
    save: 'Enregistrer',
    cancel: 'Annuler',
    divider: {
      basicDetails: 'Détails de base',
    },
    print: 'Imprimer',
    delete: 'Supprimer',
    added: 'Ajouté',
    modified: 'Modifié',
    lastUpdatedBy: 'Dernière mise à jour par',
    discardChanges: 'Annuler les modifications',
    alert: {
      areYouSure: 'Êtes-vous sûr de vouloir partir ?',
      preventExist: 'Les modifications non enregistrées seront perdues.',
      linkSent: 'Le lien a été envoyé à {{email}}',
      resendLinkSent: 'Le lien de renvoi a été envoyé à {{email}}',
    },
  },
  auth: {
    email: 'Email',
    password: 'Mot de passe',
    loginButton: 'Se connecter',
    loginTitle: 'Connexion à {{appName}}',
    loginSubtitle: 'Bienvenue ! Veuillez entrer vos identifiants',
    emailRequired: 'L’email est requis',
    invalidEmail: 'Veuillez entrer une adresse email valide',
    passwordRequired: 'Le mot de passe est requis',
    passwordTooShort: 'Le mot de passe doit comporter au moins {{length}} caractères',
    errorTitle: 'Échec de la connexion',
    invalidCredentials: 'Email ou mot de passe invalide',
    successTitle: 'Succès',
    loginSuccessful: 'Content de vous revoir !',
    genericError: 'Une erreur s’est produite. Veuillez réessayer.',
    authenticating: 'Authentification en cours...',
    emailPlaceholder: 'Entrez votre email',
    passwordPlaceholder: 'Entrez votre mot de passe',
    forgotPassword: 'Mot de passe oublié ?',
    orDivider: 'ou',
    noAccount: 'Vous n’avez pas de compte ?',
    signUpLink: 'S’inscrire',
    remainingAttempts:
      '{{count}} tentative restante avant que le compte ne soit temporairement verrouillé',
    logoutConfirmation: 'Êtes-vous sûr de vouloir vous déconnecter ?',
    redirectTxt: 'Vous serez redirigé vers la page de connexion.',

    form: {
      email: {
        label: 'E-mail',
        placeholder: 'Entrez votre e-mail',
      },
      password: {
        label: 'Mot de passe',
        placeholder: 'Entrez votre mot de passe',
        notContainWhiteSpace: "Le mot de passe ne doit pas contenir d'espace",
        invalidPassword: 'Veuillez entrer un mot de passe valide',
      },
    },
    footer: {
      terms: 'Conditions',
      privacy: 'Confidentialité',
      docs: 'Docs',
      helps: 'Aide',
    },
    forgetPasswordPage: {
      header: 'Mot de passe oublié ?',
      headerDescription:
        'Ne vous inquiétez pas ! Cela arrive. Veuillez entrer l’adresse email associée à votre compte.',
      sendOtp: 'Envoyer le code',
    },
    otpVerificationPage: {
      header: 'Vérifiez le code',
      headerDescription:
        'Veuillez entrer le code à {{length}} chiffres envoyé à votre adresse email',
      sendOtp: 'Envoyer le code',
      mustBeInLength: 'Le code doit comporter {{length}} chiffres',
      resendOtp: 'Renvoyer le code',
      requireOtp: 'Veuillez entrer le code',
      doNotGetOtp: "Vous n'avez pas reçu le code ?",
      verifyButton: 'Vérifier',
    },
    resetPasswordPage: {
      header: 'Réinitialiser le mot de passe',
      headerDescription:
        'Veuillez entrer le code à {{length}} chiffres envoyé à votre adresse email',
      resetPasswordButton: 'Réinitialiser le mot de passe',
      requiredPassword: 'Veuillez entrer un nouveau mot de passe',
      createPasswordPlaceholder: 'Créer un nouveau mot de passe',
      confirmPasswordPlaceholder: 'Confirmer le nouveau mot de passe',
      requiredConfirmPassword: 'Veuillez confirmer votre mot de passe',
      doNotMatchPassword: 'Les mots de passe ne correspondent pas',
      minChar: 'Au moins 8 caractères',
      numberOrSymbol: 'Au moins un chiffre (0-9) ou un symbole',
      upperLowerCase: 'Lettres minuscules (a-z) et majuscules (A-Z)',
      passwordStatus: {
        weak: 'Faible',
        medium: 'Moyen',
        strong: 'Fort',
      },
    },
  },
  languages: {
    EN: 'Anglais',
    FR: 'Français',
  },
  dashboard: {
    title: 'Tableau de bord',
    welcome: 'Bienvenue, {{name}} !',
    languageStatus: 'Langue actuelle : {{language}}',
    pendingDeliveries: 'Livraisons en attente',
    inTransit: 'En transit',
    deliveredToday: 'Livré aujourd’hui',
    addDelivery: 'Ajouter une livraison',
    changeUser: 'Changer d’utilisateur',
    showWarning: 'Afficher un avertissement',
    translationDemo: 'Démonstration de traduction',
    addCustomer: 'Ajouter un client',
    editCustomer: 'Modifier le Client',

    demoText:
      'Bonjour {{name}} ! Vous avez {{count}} livraisons en attente. {{isVIP ? "Vous êtes un client VIP." : "Envisagez de passer à VIP."}}',
    newCustomer: 'Nouveau client',
    duplicateCustomer: 'Client dupliqué',
    customer: {
      confirmDeleteCustomer: 'Êtes-vous sûr de vouloir supprimer {{companyName}}?',
      customerDuplicatedSuccessfully: 'Client dupliqué avec succès',
      failedToDuplicateCustomer: 'Échec de la duplication du client',
      newCustomer: 'Nouveau Client',
      duplicateCustomer: 'Dupliquer le Client',
      emailLoginDetails: 'Envoyer les Détails de Connexion par Email',
      searchCustomer: 'Rechercher un Client',
      addCustomer: 'Ajouter un Client',
      seedCustomers: 'Générer des Clients',
      failedToLoadCustomers: 'Échec du chargement des clients',
      customerDeletedSuccessfully: 'Client supprimé avec succès',
      failedToDeleteCustomer: 'Échec de la suppression du client',
      allowsInvoiceAccess:
        'Permet au contact de voir les factures et de payer en utilisant les méthodes prises en charge',
      allowsPriceAccess: 'Permet au contact de voir les prix des commandes',
      allowsAddressAccess: 'Permet au contact d’accéder à la liste des adresses',
      selectOrAddDepartment: 'Sélectionnez ou ajoutez un département',
      contactCreatedSuccessfully: 'Contact créé avec succès',
      contactUpdatedSuccessfully: 'Contact mis à jour avec succès',
      contactEmailExists: 'Un contact avec cet e-mail existe déjà',

      columns: {
        companyName: "Nom de l'entreprise",
        accountNumber: 'Numéro de compte',
        contactName: 'Nom du contact',
        addressLine1: 'Adresse ligne 1',
        city: 'Ville',
        phone: 'Téléphone',
        email: 'E-mail',
        fax: 'Fax',
        status: 'Statut',
        category: 'Catégorie',
        dateAdded: "Date d'ajout",
        dateUpdated: 'Date de mise à jour',
        lastUpdateBy: 'Dernière mise à jour par',
        action: 'Action',
        department: 'Département',
        selectAppropriateDepartment: 'Sélectionnez le département approprié',
        selectOrAddDepartment: 'Sélectionnez ou ajoutez un département',
        contactActive: 'Contact Actif',
        permissionSetting: 'Paramètre d’Autorisation',
        permissionSettingDescription:
          'Contrôlez quelles informations et actions ce contact peut accéder.',
        invoices: 'Factures',
        selectAppropriateCategory: 'Sélectionnez la catégorie appropriée',
        permission: 'Autorisation',
        contactDeletedSuccessfully: 'Contact supprimé avec succès',
        deleteContact: 'Êtes-vous sûr de vouloir supprimer ce contact ?',
        confirmDeleteContact:
          'Cette action est irréversible. La suppression de ce contact supprimera tous ses détails et son historique de vos dossiers.',
        active: 'Actif',
        inactive: 'Inactif',
        invoice: 'Facture',
        failedToLoadContacts: 'Échec du chargement des contacts',
        enterNewContactDetails: 'Entrez les détails du nouveau contact ci-dessous',
        searchContact: 'Rechercher un Contact',
        updateContact: 'Mettre à Jour le Contact',
        addContact: 'Ajouter un Contact',
        sendCredentials: 'Envoyer les Identifiants',
        pleaseEnterYourName: 'Veuillez entrer votre nom',
        errorFetchingCountry: 'Erreur lors de la récupération du pays :',
        okay: 'D’accord',
        customerEnabledSuccessfully: 'Client activé avec succès',
        customerDisabledSuccessfully: 'Client désactivé avec succès',
        confirmEnableCustomer: 'Êtes-vous sûr de vouloir activer ce client ?',
        confirmDisableCustomer: 'Êtes-vous sûr de vouloir désactiver ce client ?',
        enableCustomerDescription:
          'Activer ce client permettra son accès au portail et aux services.',
        disableCustomerDescription:
          'Désactiver ce client révoquera son accès au portail et aux services, mais vous pourrez toujours accéder à ses commandes et informations financières.',
        customerUpdatedSuccessfully: 'Client mis à jour avec succès',
        failedToCreateCustomer: 'Échec de la création du client',
        customerCreatedSuccessfully: 'Client créé avec succès',
        pleaseEnterAccountNumber: 'Veuillez entrer votre numéro de compte',
        pleaseEnterValidURL: 'Veuillez entrer une URL valide',
        selectOrAddCategory: 'Sélectionnez ou ajoutez une catégorie',
        addressDetails: 'Détails de l’adresse',
        faxNumber: 'Numéro de Fax',
        website: 'Site Web',
        customerIsActive: 'Statut du Client',
        pleaseEnterYourAddress: 'Veuillez entrer votre adresse',
        pleaseEnterYourCity: 'Veuillez entrer votre ville',
        pleaseEnterYourProvince: 'Veuillez entrer votre province',
        pleaseEnterYourPostalCode: 'Veuillez entrer votre code postal',
        pleaseEnterYourCountry: 'Veuillez entrer votre pays',
        toggleCustomerStatus: 'Utilisez ce bouton pour activer ou désactiver un client :',
        enabled: 'Activé',
        enabledDescription:
          'Le client peut se connecter, passer des commandes et recevoir des mises à jour.',
        disabled: 'Désactivé',
        disabledDescription:
          'Le client est restreint d’accès, mais ses données restent pour les archives.',
        activeOrdersNote: 'Les commandes actives ne seront pas affectées.',
        activate: 'Activer',
        deactivate: 'Désactiver',
        resendCredentials: 'Renvoyer les Identifiants',
        formFieldNames: {
          companyName: "Nom de l'entreprise",
          contactName: 'Nom du contact',
          accountNumber: 'Numéro de compte',
          addressLine1: 'Adresse ligne 1',
          addressLine2: 'Adresse ligne 2',
          dateAdded: "Date d'ajout",
          dateUpdated: 'Date de mise à jour',
          lastUpdateBy: 'Dernière mise à jour par',
          contactActive: 'Contact actif',
          permissionSetting: 'Paramètre d’Autorisation',
          deleteContact: 'Supprimer le Contact',
        },
      },
      contactsEmptyState: {
        title: 'Aucun client trouvé',
        description: 'Pour commencer',
        link: 'ajouter un nouveau contact',
      },
      settings: {
        notification: 'Notification',
        uiConfiguration: 'Configuration UI',
        field: 'Field',
        required: 'Required',
        visible: 'Visible',
        general: {
          generalSettings: 'Paramètres Généraux',
          commonSettingsDescription:
            'Tous les paramètres communs que vous pouvez personnaliser selon vos préférences sur différents canaux.',
          prepaidOrders: 'Commandes Prépayées',
          saveSettings: 'Enregistrer les Paramètres',
          prepaidOrdersInfo:
            'Les commandes prépayées signifient qu’un paiement est requis avant de finaliser la commande. Si cette option n’est pas sélectionnée, le client sera facturé selon la méthode de facturation mensuelle automatisée par défaut.',
          generalSettingsUpdated: 'Paramètres généraux mis à jour avec succès',
        },
        settingsNotification: {
          sms: 'SMS',
          email: 'Email',
          push: 'Push',
          inApp: 'Dans l’Application',
          notificationSettings: 'Paramètres de Notifications',
          notificationSettingsDescription:
            'Personnalisez vos préférences de notification sur différents canaux.',
          disableAll: 'Désactiver Tout',
          enableAll: 'Activer Tout',
          saveSettings: 'Enregistrer les Paramètres',
          formField: 'Champ de Formulaire',
          formFieldDescription: 'Personnalisez vos champs de formulaire selon vos besoins.',
          delivery: 'Livraison',
          collection: 'Collecte',
          name: 'Nom',
          required: 'Requis',
          visible: 'Visible',
          packagingType: 'Type d’Emballage',
          weight: 'Poids',
          width: 'Largeur',
          height: 'Hauteur',
          length: 'Longueur',
          maxWeight: 'Poids Maximum',
          editPackageTypeInfo: 'Modifier les Informations du Type d’Emballage',
          enterNewPackageTypeDetails: 'Entrez les détails du nouveau type d’emballage ci-dessous',
          basicDetails: 'Détails de Base',
          packageType: 'Type de Colis',
          notificationSettingsUpdated: 'Paramètres de notification mis à jour avec succès',
          orderCreated:
            "Déclenché lorsqu'une nouvelle commande est créée avec succès dans le système.",
          orderUpdated:
            'Déclenché lorsque des modifications sont apportées à une commande existante.',
          orderCancelled:
            "Déclenché lorsqu'une commande est annulée par l'utilisateur ou l'administrateur.",
          orderPickedUp:
            "Déclenché lorsque le coursier récupère la commande auprès de l'expéditeur.",
          orderDelivered: 'Déclenché lorsque la commande est livrée avec succès au destinataire.',
        },
        settingsUIConfiguration: {
          uiConfigUpdated:
            'Paramètres de configuration de l’interface utilisateur mis à jour avec succès',
        },
        settingsMainModule: {
          accountSettings: 'Paramètres du compte',
          operationSettings: 'Paramètres des opérations',
          users: 'Utilisateurs',
          importExport: 'Importer/Exporter',
        },
      },
      emptyState: {
        title: 'Aucun client trouvé',
        description: 'Veuillez ajouter un nouveau client',
        link: 'Ajouter un client',
      },
      services: {
        colDefs: {
          serviceName: 'Nom du service',
          serviceLevel: 'Niveau de service',
        },
        emptyState: {
          title: 'Aucun service trouvé',
          description: 'Veuillez ajouter un nouveau service',
          link: 'Ajouter un service',
        },
        searchPlaceholder: 'Rechercher un service',
        assignServices: 'Attribuer des Services',
        searchServiceName: 'Rechercher le nom du service',
        unassignedServices: 'Services non attribués',
        noServices: 'Aucun service',
        noServicesAvailableToAssign: 'Aucun service disponible à attribuer',
        assignedServices: 'Services attribués',
        noServicesAssigned: 'Aucun service attribué',
        noServicesAvailable: 'Aucun service disponible',
      },
    },
  },
  notifications: {
    languageChanged: 'Langue changée en {{language}}',
    languageChangeDescription: 'La langue de l’interface a été mise à jour.',
    deliveryAdded: 'Nouvelle livraison ajoutée',
    currentDeliveries: 'Livraisons en attente actuelles : {{count}}',
    userChanged: 'Utilisateur changé en {{name}}',
    welcomeUser: 'Bienvenue, {{name}} ! Profitez de votre expérience sur le tableau de bord.',
    warning: 'Avertissement',
    warningDescription: 'Cette action est irréversible. Veuillez procéder avec prudence.',
  },
  contextMenuItems: {
    customer: {
      open: 'Ouvrir',
      assignTo: 'Assigner à',
      assignToSubA: 'conducteur A',
      assignToSubB: 'conducteur B',
      updateStatus: 'Mettre à jour le statut',
    },
  },
  sidebar: {
    customer: 'Client',
    customers: 'Clients',
    partner: 'Partenaire',
    billing: 'Facturation',
    logistic: 'Logistique',
    orders: 'Commandes',
    prices: 'Prix',
    dispatcher: 'Dispatcheur',
    routes: 'Itinéraires',
    vehicle: 'Véhicule',
    location: 'Emplacement',
    locations: 'Emplacements',
    settings: 'Paramètres',
    general: 'Général',
    partners: 'Partenaires',
    templates: 'Modèles',
    address: 'Adresse',
    zone: 'Zone',
    zoneLookupTable: 'Zone Table',
    pricesSets: 'Ensembles de prix',
    pricesModifiers: 'Modificateurs de prix',
    addressAccess: 'Accès aux Adresses',
    pricesAccess: 'Accès aux Prix',
    invoiceAccess: 'Accès aux Factures',
    demo: 'Démo',
    historyGrid: "Grille d'historique",
    orderHistory: 'Historique des commandes',
  },
  paymentPage: {
    header: {
      title: 'Paiements',
      searchPlaceholder: 'Rechercher un paiement',
      addPaymentBtn: 'Ajouter un paiement',
    },
    colDefs: {
      customer: 'Client',
      referenceNumber: 'Numéro de référence',
      date: 'Date',
      amount: 'Montant',
      invoiceNumber: 'Numéro de facture',
      type: 'Type',
      created: 'Créé',
      action: 'Action',
    },
    contextMenu: {
      addPayment: 'Ajouter un paiement',
      download: 'Télécharger',
    },
    alert: {
      confirmDelete: 'Êtes-vous sûr de vouloir supprimer ce paiement ?',
      confirmDeleteMessage:
        'cette action ne peut pas être annulée. La suppression de ce paiement supprimera tous leurs détails et historique de vos dossiers.',
    },
    emptyState: {
      title: 'Aucun paiement trouvé',
      description: 'Pour commencer,',
      link: 'Ajouter un nouveau paiement',
    },
  },
  paymentOperationsPage: {
    header: {
      addTitle: 'Ajouter un paiement',
      editTitle: 'Modifier le paiement',
    },
    form: {
      labels: {
        customer: 'Client',
        paymentMethod: 'Méthode de paiement',
        dateReceived: 'Date de réception',
        paymentAmount: 'Montant du paiement',
        addCredit: 'Ajouter un crédit',
        memo: 'Mémo',
        referenceNumber: 'Numéro de référence',
      },
      placeholders: {
        selectPaymentMethod: 'Sélectionner la méthode de paiement',
        memoPlaceholder: 'Écrivez votre note ici...',
        amountPlaceholder: '$0.00',
        referenceNumberPlaceholder: '012345678901',
      },
      validation: {
        paymentMethodRequired: 'La méthode de paiement est requise',
        dateReceivedRequired: 'La date de réception est requise',
        paymentAmountRequired: 'Le montant du paiement est requis',
        creditAmountRequired: 'Le montant du crédit est requis',
        referenceNumberRequired: 'Le numéro de référence est requis',
      },
    },
    invoicesGrid: {
      colDefs: {
        invoiceNumber: 'Numéro de facture',
        date: 'Date',
        amount: 'Montant',
        amountDue: 'Montant dû',
      },
      emptyState: {
        title: 'Aucune facture trouvée',
        description: 'Aucune facture trouvée pour ce client',
      },
    },
    paymentMethods: {
      creditCard: 'Carte de crédit - Solutions SecurePay',
      paypal: 'PayPal - Services InstantPay',
      bankTransfer: 'Virement bancaire - Réseau SwiftBanking',
      credit: 'Crédit',
    },
  },
  columnMange: {
    title: 'Titre',
    uncheckColumn: 'Décochez le nom de la colonne pour la masquer.',
    selectAll: 'Tout sélectionner',
    resetToDefault: 'Réinitialiser par défaut',
    apply: 'Appliquer',
    noColumns: 'Aucune colonne',
    keepVisibleWarn: 'Veuillez laisser au moins une colonne visible',
    errorWhileUpdating: "Une erreur s'est produite lors de la mise à jour des colonnes",
    columnManagerTooltip:
      "Réorganisez les colonnes et activez ou désactivez leur visibilité pour personnaliser l'affichage de votre grille.",
  },
  historyGrid: {
    property: 'Propriété',
    oldValue: 'Ancienne valeur',
    newValue: 'Nouvelle valeur',
    dateTime: 'Date et heure',
    modifiedBy: 'Modifié par',
    title: 'Historique',
    noHistoryFound: 'Aucun historique trouvé',
  },
  searchFilterBox: {
    apply: 'Appliquer',
    clearAll: 'Tout effacer',
    addFilter: 'Ajouter un filtre',
    selectOption: 'Veuillez sélectionner une option',
    selectCondition: 'Sélectionner une condition',
    enterValue: 'Veuillez entrer une valeur',
    noMultiSpaces: 'Espaces multiples non autorisés',
    enterInput: 'Veuillez entrer une saisie',
    enterNumber: 'Veuillez entrer un nombre',
    filterNumber: 'Entrez le nombre à filtrer',
    selectDate: 'Veuillez sélectionner une date',
    selectAnyOne: 'Veuillez en sélectionner un',
    selectField: 'Sélectionner un champ',
    advancedFilter: 'Filtre avancé',
    selectFields: 'Sélectionner des champs',
    setAsQuickFilter: 'Définir comme filtre rapide',

    operators: {
      string: {
        contains: 'Contient',
        notContains: 'Ne contient pas',
        startsWith: 'Commence par',
        endsWith: 'Se termine par',
        equals: 'Égal à',
        notEquals: 'Différent de',
      },
      number: {
        equals: 'Égal à',
        notEquals: 'Différent de',
        greaterThan: 'Supérieur à',
        lessThan: 'Inférieur à',
        greaterThanOrEqual: 'Supérieur ou égal à',
        lessThanOrEqual: 'Inférieur ou égal à',
      },
    },
  },
  customerAddressPage: {
    addAddress: 'Ajouter une adresse',
    searchPlaceholder: 'Rechercher une adresse',
    modal: {
      addAddress: 'Ajouter adresse',
      editAddress: 'Afficher l’adresse',
      AddAddressDescription: 'Remplissez les détails pour ajouter une nouvelle adresse.',
      EditAddressDescription: 'Modifiez les détails de votre adresse ci-dessous.',
    },
    emptyState: {
      title: 'Aucune adresse trouvée',
      description: 'Veuillez ajouter une nouvelle adresse',
      link: 'Ajouter une adresse',
    },
    colDefs: {
      name: 'Nom',
      companyName: 'Nom de l’entreprise',
      addressLine1: 'Adresse ligne 1',
      addressLine2: 'Adresse ligne 2',
      phone: 'Téléphone',
      city: 'Ville',
      postalCode: 'Code postal',
      email: 'Email',
      action: 'Action',
    },
    contextMenu: {
      newAddress: 'Nouvelle adresse',
      duplicateAddress: 'Dupliquer l’adresse',
    },
    notifications: {
      confirmDelete: 'Êtes-vous sûr de vouloir supprimer cette adresse ?',
      successDelete: 'Adresse supprimée avec succès',
      failedDelete: 'Échec de la suppression de l’adresse',
      confirmDeleteMessage:
        'Cette action est irréversible. Supprimer ce client supprimera tous ses détails et son historique de vos enregistrements.',
      confirmDuplicate: 'Êtes-vous sûr de vouloir dupliquer cette adresse ?',
      confirmDuplicateMessage: 'Une entrée dupliquée sera créée.',
      successCreate: 'Adresse créée avec succès',
      successUpdate: 'Adresse mise à jour avec succès',
      successDuplicated: 'Adresse dupliquée avec succès',
      failedDuplicate: 'Échec de la duplication de l’adresse',
    },
    operationalForm: {
      customer: 'Client',
      customerPlaceholder: 'Sélectionnez un client',
      customerError: 'Veuillez sélectionner un client',
      name: 'Nom',
      namePlaceholder: 'Jean Dupont',
      nameError: 'Le nom est requis',
      companyName: 'Nom de l’entreprise',
      companyNamePlaceholder: 'abc pvt.ltd.',
      companyNameError: 'Le nom de l’entreprise est requis',
      email: 'Email',
      emailPlaceholder: '<EMAIL>',
      emailError: 'L’email est requis',
      emailTypeError: 'Entrez un email valide',
      phoneNumber: 'Numéro de téléphone',
      phoneNumberPlaceholder: '(*************',
      phoneNumberError: 'Le numéro de téléphone est requis',
      validPhoneNumberError: 'Veuillez entrer un numéro de téléphone valide.',
      enterValidFaxNumber: 'Veuillez entrer un numéro de fax valide',

      phoneExt: 'Extension téléphonique',
      phoneExtPlaceholder: '567',
      phoneExtError: 'L’extension téléphonique est requise',
      locationDividerText: 'Détails de localisation',
      addressLine1: 'Adresse Ligne 1',
      addressLine1Placeholder: '123 rue Principale',
      addressLine1Error: 'Adresse Ligne 1 est requis',
      addressLine2: 'Adresse Ligne 2',
      addressLine2Placeholder: '123 rue Principale',
      city: 'Ville',
      cityError: 'La ville est requise',
      cityPlaceholder: 'E.g. Saint rue principale',
      province: 'Province',
      provinceError: 'La province est requise',
      provincePlaceholder: 'E.g. Montréal',
      postalCode: 'Code postal',
      postalCodePlaceholder: 'E.g. H4Y H4M',
      postalCodeError: 'Le code postal est requis',
      validPostalCodeError: 'Veuillez entrer un code postal valide',
      country: 'Pays',
      countryPlaceholder: 'E.g. Canada',
      countryError: 'Le pays est requis',
      zone: 'Zone',
      zonePlaceholder: 'Zone Y',
      zoneError: 'La zone est requise',
      comments: 'Notes',
      commentsDividerTex: 'Notes',
      commentsPlaceHolder: 'Écrivez votre note ici...',
      notesTooltip:
        'Utilisez ce champ pour ajouter des informations supplémentaires liées à l’adresse.',
      zoneToolTip:
        "Votre zone de livraison est automatiquement déterminée par votre code postal. Veuillez vous assurer que votre code postal se trouve dans notre zone de service avant d'ajouter une adresse.",
    },
  },

  vehiclePage: {
    header: {
      title: 'Véhicule',
      addNewVehicleBtnText: 'Ajouter un nouveau véhicule',
      addNewTimeClockSessionBtnText: 'Ajouter une session',
      searchSession: 'Search sessions',
      searchVehicle: 'Search Véhicule',
    },
    form: {
      totalDistance: 'Distance totale',
      sameTimeError: 'Impossible d’ajouter une session avec la même date et heure',
    },
    timeClockSessionForm: {
      driverName: {
        label: 'Nom du chauffeur',
        errorMessage: 'Veuillez sélectionner un chauffeur',
        placeholder: 'Sélectionner un chauffeur',
      },
      distance: {
        label: 'Distance',
        errorMessage: 'La distance est requise',
      },
      dateTime: {
        label: 'Date et heure',
        errorMessage: "Veuillez sélectionner la date et l'heure",
      },
      manual: 'Manuel',
      automatic: 'Automatique',
    },
    tabs: {
      general: 'Général',
      timeClockSession: 'Sessions de pointage',
      tooltipText: 'Veuillez ajouter un véhicule pour accéder à cet onglet.',
    },
    emptyState: {
      title: 'Aucun véhicule trouvé',
      description: 'Créer un nouveau véhicule',
      link: 'Ajouter un véhicule',
      timeClockTitle: 'Aucune session trouvée',
      timeClockDescription: 'Créer une nouvelle session',
      timeClockLink: 'Créer une session',
    },
    notificationMessages: {
      successAdded: 'Véhicule ajouté avec succès',
      successDelete: 'Véhicule supprimé avec succès',
      successUpdate: 'Véhicule mis à jour avec succès',
      sessionSuccessAdded: 'Session ajoutée avec succès',
      sessionSuccessDelete: 'Session supprimée avec succès',
      sessionSuccessUpdate: 'Session mise à jour avec succès',
      failedToLoad: 'Échec du chargement des véhicules',
      failedToAdd: "Échec de l'ajout du nouveau véhicule",
      failedDelete: 'Échec de la suppression du véhicule',
      failedUpdate: 'Échec de la mise à jour du véhicule',
      failedSessionUpdate: 'Échec de la mise à jour de la session',
      failedSessionSessionAdd: "Échec de l'ajout de la nouvelle session",
      failedSessionDelete: 'Échec de la suppression de la session',
      sessionOverlaps: 'La session de pointage chevauche une session existante',
      sessionHourLimitExceed:
        'La durée de la session de pointage dépasse la limite autorisée de {{maxDuration}} heures',
    },
    noVehiclesFound: 'Aucun véhicule trouvé',
    createNewVehicle: 'Créez un nouveau véhicule pour commencer',
    noSessionFound: 'Aucune session trouvée',
    createNewSession: 'Créez une nouvelle session pour commencer',
    generalHeader: {
      add: 'Ajouter un véhicule',
      edit: 'Modifier le véhicule',
    },
    modal: {
      addSession: 'Ajouter une session',
      editSession: 'Modifier la session de {{name}}',
      headerDesc: 'Les champs suivants permettent d’ajouter une nouvelle entrée.',
      headerDescEdit: 'Les champs suivants permettent de modifier la session.',
    },
    breedCrumbs: {
      vehicle: 'Véhicule',
      general: 'Général',
      timeClockSession: 'Sessions de pointage',
    },
    alert: {
      deleteConfirmation: 'Êtes-vous sûr de vouloir supprimer ce véhicule ?',
      deleteConfirmationMessage:
        'Cette action est irréversible. Supprimer ce véhicule supprimera tous ses détails et son historique de vos enregistrements.',
      sessionDeleteConfirmation: 'Êtes-vous sûr de vouloir supprimer cette session ?',
      sessionDeleteConfirmationMessage:
        'Cette action est irréversible. Supprimer cette session supprimera tous ses détails et son historique de vos enregistrements.',
      firstBtnText: 'Supprimer',
      secondBtnText: 'Annuler',
    },
    tooltip: {
      packageType:
        'Sélectionnez les types de colis que le véhicule peut transporter : Boîtes, palettes ou personnalisé.',
      capacity: 'Saisissez la capacité maximale de poids du véhicule (ex. : 3000 lb).',
      branches:
        "Indiquez l'emplacement ou la succursale à laquelle le véhicule est affecté. Cela permet d'identifier sa base d'opérations et de gestion logistique.",
      ownedBy:
        "Saisissez le nom du propriétaire du véhicule, qu'il s'agisse d'un particulier ou d'une entreprise.",
      notes:
        "Notez tous les détails supplémentaires ou observations sur le véhicule, comme des instructions spéciales, des besoins d'entretien ou des conditions d'exploitation.",
    },
    colDefs: {
      entryDate: 'Date d’entrée',
      type: 'Type',
      capacity: 'Capacité',
      fleetId: 'ID de la flotte',
      odometer: 'Lecture de l’odomètre',
      licensePlate: 'Plaque d’immatriculation',
      make: 'Marque',
      model: 'Modèle',
      year: 'Année',
      defaultDriver: 'Conducteur par défaut',
      action: 'Action',
    },
    labels: {
      basicDetails: 'Détails de base',
      vehicleType: 'Type de véhicule',
      fleetId: 'ID de flotte',
      make: 'Marque',
      model: 'Modèle',
      year: 'Année',
      license: 'Licence / Étiquette',
      vin: 'VIN',
      packageType: 'Type de colis',
      capabilities: 'Capacités',
      capacity: 'Capacité',
      branches: 'Succursales',
      odometer: 'Odomètre',
      ownedBy: 'Propriétaire',
      defaultDriver: 'Conducteur par défaut',
      commentSection: 'Commentaire',
      comments: 'Commentaires',
    },
    placeholders: {
      selectType: 'Sélectionnez le type',
      fleetId: 'CAR001',
      make: 'Dodge',
      model: 'Caravan',
      year: '1999',
      license: 'H4G 1NX',
      vin: 'D12154345334AS67FS77D',
      capabilities: 'Boîtes',
      capacity: '3000',
      branches: 'Succursale de Montréal',
      odometer: '15202',
      ownedBy: 'VNP',
      selectDefaultDriver: 'Sélectionnez le conducteur par défaut',
      comments: 'Écrivez votre commentaire ici...',
    },
    messages: {
      vehicleTypeRequired: 'Le type de véhicule est requis',
      fleetIdRequired: "L'identifiant de flotte est requis",
      makeRequired: 'La marque est requise',
      modelRequired: 'Le modèle est requis',
      yearRequired: "L'année est requise",
      maxYearExceeded: "L'année maximale est dépassée",
      licenseRequired: 'La licence / étiquette est requise',
      vinRequired: 'Le VIN est requis',
      packageTypeRequired: 'Le type de colis est requis',
      capabilitiesRequired: 'Les capacités sont requises',
      capacityRequired: 'La capacité est requise',
      branchesRequired: 'Les succursales sont requises',
      odometerRequired: "L'odomètre est requis",
      ownedByRequired: 'Le propriétaire est requis',
      defaultDriverRequired: 'Le conducteur par défaut est requis',
      requireEndDate: 'Veuillez sélectionner la date de fin',
      vehicleNotFound: 'Véhicule non trouvé',
      licensePlateExists: 'Un véhicule avec cette plaque d’immatriculation existe déjà',
      invalidDecimal: 'Veuillez entrer un nombre valide avec 2 décimales (ex. : 12, 12.3 ou 12.34)',
    },
    alerts: {
      discardChangesTitle: 'Êtes-vous sûr de vouloir annuler les modifications ?',
      discardChangesMessage: 'Les modifications seront réinitialisées à leur valeur par défaut',
      discardButtonTitle: 'Annuler',
    },
    contextMenuItems: {
      newVehicle: 'Ajouter un véhicule',
      duplicateVehicle: 'Dupliquer le véhicule',
      delete: 'Supprimer',
    },
    timeClockColDefs: {
      driverName: 'Nom du chauffeur',
      vehicle: 'Véhicule',
      distance: 'Distance',
      startTime: 'Date et heure de début',
      endTime: 'Date et heure de fin',
      totalTime: 'Temps total',
      action: 'Action',
    },
    addNameSession: 'Ajouter la session {{name}}',
    activeSession: 'Session active',
    inactiveSession: 'Session Incomplete',
    totalTimeWarning: 'Veuillez ajouter la date de fin pour afficher le temps total',
    totalTime: 'Temps total',
    startTime: 'Heure de début',
    endTime: 'Heure de fin',
    minDistance: 'La distance doit être supérieure à 1 km',
  },
  addressPage: {
    header: {
      title: 'Adresse',
      searchAddress: 'Rechercher une adresse',
      addAddress: 'Ajouter une adresse',
    },
    contextMenu: {
      newAddress: 'Nouvelle adresse',
      duplicateAddress: 'Dupliquer l’adresse',
    },
    notification: {
      successAddressDelete: 'Adresse supprimée avec succès',
      successAddressAdd: 'Adresse créée avec succès',
      successAddressUpdate: 'Adresse mise à jour avec succès',
      successAddressDuplicate: 'Adresse dupliquée avec succès',
      failedAddressDelete: 'Échec de la suppression de l’adresse',
      failedAddressAdd: 'Échec de la création de l’adresse',
      failedAddressUpdate: 'Échec de la mise à jour de l’adresse',
      failedDuplicateUpdate: 'Échec de la duplication de l’adresse',
      cantDeleteDefaultAddress: 'Impossible de supprimer une adresse par défaut',
      notFound: 'Adresse non trouvée',
      validationFailed: 'Échec de la validation',
      addressIdNotFound: 'ID de l’adresse non trouvé, veuillez réessayer',
    },

    alert: {
      deleteConfirmation: 'Êtes-vous sûr de vouloir supprimer cette adresse ?',
      deleteConfirmationMessage:
        'Cette action est irréversible. Supprimer cette adresse supprimera tous ses détails et son historique de vos enregistrements.',
    },
    modal: {
      addAddress: 'Ajouter une adresse',
      editAddress: 'Afficher et modifier l’adresse',
      AddAddressDescription: 'Remplissez les détails pour ajouter une nouvelle adresse.',
      EditAddressDescription: 'Modifiez les détails de votre adresse ci-dessous.',
    },

    emptyState: {
      title: 'Aucune adresse trouvée',
      description: 'Veuillez ajouter une nouvelle adresse',
      link: 'Ajouter une adresse',
    },
    colDefs: {
      customer: 'Client',
      name: 'Nom',
      contact: 'Contact',
      addressLine1: 'Adresse ligne 1',
      addressLine2: 'Adresse ligne 2',
      city: 'Ville',
      phone: 'Phone',
      companyName: 'Company',
      province: 'Province',
      postalCode: 'Code postal',
      email: 'Email',
      zone: 'Zone',
      action: 'Action',
    },
    operationalForm: {
      customer: 'Client nom',
      customerPlaceholder: 'Sélectionner un client',
      customerError: 'Veuillez sélectionner un client',
      name: 'Nom',
      namePlaceholder: 'John Doe',
      nameError: 'Le nom est requis',
      companyName: 'Nom de l’entreprise',
      companyNamePlaceholder: 'ABC SARL',
      companyNameError: 'Le nom de l’entreprise est requis',
      email: 'Email',
      emailPlaceholder: '<EMAIL>',
      emailError: 'L’email est requis',
      emailTypeError: 'Entrez un email valide',
      phoneNumber: 'Numéro de téléphone',
      phoneNumberPlaceholder: '(*************',
      phoneNumberError: 'Le numéro de téléphone est requis',
      validPhoneNumberError: 'Veuillez entrer un numéro de téléphone valide.',
      phoneExt: 'Extension de téléphone',
      phoneExtPlaceholder: '00000',
      phoneExtError: 'L’extension de téléphone est requise',
      locationDividerText: 'Détails de l’emplacement',
      addressLine1: 'Adresse ligne 1',
      addressLine1Placeholder: '123 Rue Principale',
      addressLine1Error: 'Adresse ligne 1 est requise',
      addressLine2: 'Adresse ligne 2',
      addressLine2Placeholder: 'Apt 2',
      city: 'Ville',
      cityError: 'La ville est requise',
      cityPlaceholder: 'E.g. Montréal',
      province: 'Province',
      provinceError: 'La province est requise',
      provincePlaceholder: 'E.g. Québec',
      postalCode: 'Code postal',
      postalCodePlaceholder: 'E.g. H4Y H4M',
      postalCodeError: 'Le code postal est requis',
      validPostalCodeError: 'Veuillez entrer un code postal valide',
      country: 'Pays',
      countryPlaceholder: 'E.g. Canada',
      countryError: 'Le pays est requis',
      zone: 'Zone',
      zonePlaceholder: 'Ex. Zone Y',
      zoneError: 'Aucune zone trouvée avec le code postal {{code}} donné.',
      comments: 'Notes',
      commentsDividerTex: 'Notes',
      commentsPlaceHolder: 'Écrivez votre note ici...',
      noPostalCodeFound: 'Aucun code postal trouvé pour cette adresse afin de charger la zone',
      noCoordinatesFound: 'Aucune coordonnée trouvée pour cette adresse',
    },
  },
  zonePage: {
    newZone: 'Nouvelle zone',
    editZone: 'Modifier la zone',
    duplicateZone: 'Dupliquer la zone',

    addZone: 'Ajouter une zone',
    enterDetails: 'Entrez les détails de la nouvelle zone ci-dessous.',
    basicDetails: 'Détails de base',
    seedZones: 'Zones de départ',
    noZonesFound: 'Aucune zone trouvée',
    createNewZone: 'Créez une nouvelle zone pour commencer',
    failedToLoadZones: 'Échec du chargement des zones',
    confirmDeleteZone: 'Êtes-vous sûr de vouloir supprimer la zone this ?',
    atLeastOnePostalCode: 'Au moins un code postal est requis.',
    nameValidation: 'Le nom ne doit contenir que des lettres, des chiffres et des espaces.',
    ok: 'Ok',
    gotIt: 'Compris',
    createZoneGuidelines: 'Créer une Zone - Directives',
    click: 'Cliquer',
    pressThe: 'Appuyez sur le',
    buttonToBegin: 'bouton pour commencer.',

    fillInTheDetails: 'Remplissez les Détails',
    zoneName: 'Nom de la Zone :',
    zoneNameField: 'Nom de la zone',
    enterUniqueZoneName: 'Entrez un nom unique pour votre zone.',
    postalCodes: 'Codes Postaux :',
    guidelinesForZones: 'Directives et informations pour la création et la gestion des zones.',

    addMultiplePostalCodes:
      'Entrez un ou plusieurs codes postaux, séparés par des virgules ou appuyez sur Entrée pour ajouter. Les codes postaux déjà attribués à une autre zone seront mis en surbrillance en bleu pour indiquer un chevauchement.',
    notes: 'Remarques :',
    addAdditionalInfo: 'Ajoutez toute information supplémentaire sur cette zone (optionnel).',
    saveYourZone: 'Enregistrez votre zone',
    clickSaveToFinalize:
      'Une fois tous les détails ajoutés, cliquez sur Enregistrer pour finaliser la création de la zone.',
    howZoneWorks: 'Comment fonctionne la zone ?',
    codeIsAddedInAnotherZone: '{{code}} est également ajouté dans une autre zone.',
    codesAreAddedInAnotherZone: '{{code}} sont également ajoutés dans une autre zone.',

    notification: {
      successAdded: 'Zone créée avec succès',
      successUpdated: 'Zone mise à jour avec succès',
      successDeleted: 'Zone supprimée avec succès',
    },

    zoneLookUp: {
      hoverOverACell: 'Survolez une cellule pour voir son intersection.',
      pricingApplied: 'Tarification réciproque appliquée avec succès',
      formRevertedToOriginal: "Le formulaire a été rétabli dans son état d'origine.",
      failedToLoad: 'Échec du chargement des données',
      tableNotFound: 'Table de recherche des zones introuvable',
      emptyCellsFilled: 'Cellules vides remplies',
      allCellsAdjusted: 'Toutes les cellules ajustées',
      tableShouldNotBeEmpty:
        'Le nom de la table des zones ne peut pas être vide. Veuillez fournir un nom unique',
      changesSavedToTable:
        'Modifications enregistrées avec succès dans la table de recherche des zones',
      zoneLookUpTableSaved: 'Table de recherche des zones enregistrée avec succès',
      anErrorOccurredWhileSaving:
        "Une erreur s'est produite lors de l'enregistrement. Veuillez réessayer ou vérifier votre connexion",
      changesDiscarded: 'Modifications annulées',
      zones: 'Zones',
      enterValue: 'Saisir une valeur',
      howToUse: 'Comment utiliser la table de recherche des zones',
      gotIt: 'Compris',
      keyboardNavigation: 'Navigation au clavier',
      click: 'Cliquez',
      activateCell: "sur une cellule pour l'activer et la modifier.",
      useThe: 'Utilisez le ',
      arrowKeys: 'Flèches directionnelles',
      whenCellIsActive: " pour se déplacer entre les cellules lorsqu'une cellule est active.",
      press: 'Appuyez sur',
      escape: 'Échap',
      deactivateCell: 'pour désactiver la cellule actuelle.',
      tipsForUsage: 'Conseils pour une utilisation efficace',
      hoverBefore: 'Survolez',
      hoverStrong: 'les',
      hoverAfter: 'cellules pour voir leur intersection et une meilleure clarté.',
      applyReciprocalPricingBefore: 'Utilisez',
      applyReciprocalPricingStrong: 'Appliquer la tarification réciproque',
      applyReciprocalPricingAfter:
        'pour remplir rapidement les cellules symétriques correspondantes (par exemple, Zone A → Zone B remplira également Zone B → Zone A).',
      autoFillEmptyCellsBefore: 'Utilisez',
      autoFillEmptyCellsStrong: 'Remplissage automatique des cellules vides',
      autoFillEmptyCellsAfter:
        'pour définir une valeur par défaut pour toutes les cellules vides de la grille.',
      bulkAdjustGridValuesBefore: 'Utilisez',
      bulkAdjustGridValuesStrong: 'Ajustement massif des valeurs de la grille',
      bulkAdjustGridValuesAfter:
        'pour appliquer un ajustement fixe ou en pourcentage à toutes les cellules remplies.',
      advancedFeatures: 'Fonctionnalités avancées',
      keyboardShortcutsBefore: 'Raccourcis clavier : ',
      useShortcuts: 'Utilisez les raccourcis pour une navigation et des actions plus rapides.',
      ctrlZBefore: 'Ctrl + Z : ',
      ctrlZAfter: 'Annuler les modifications.',
      ctrlYBefore: 'Ctrl + Y : ',
      ctrlYAfter: 'Rétablir les modifications.',
      bulkAdjustModalTitle: 'Ajuster en masse les valeurs de la grille',
      bulkAdjustModalDescription:
        'Ajustez toutes les cellules peuplées dans la grille des prix par ce montant.',

      adjustmentAmountLabel: "Montant de l'ajustement (Exemple: 8,75)",
      treatAsFixedLabel: 'Traiter comme fixe',
      treatAsPercentageLabel: 'Traiter comme un pourcentage',
      autoFillEmptyCellsModalDescription:
        'Cela remplira toutes les cellules vides dans la grille avec la valeur que vous spécifiez.',
      autoFillEmptyCellsModalPlaceholder:
        'Entrez une valeur pour remplir toutes les cellules vides :',
      createZoneLookupTable: 'Créer un tableau de recherche de zones',
      editZoneLookupTable: 'Modifier le tableau de recherche de zones',
      enterUniqueName: 'Transit app zone table',
      help: 'Aide',
      applyReciprocalPricing: 'Appliquer la tarification réciproque',
      autoFillEmptyCells: 'Remplissage automatique des cellules vides',
      autoFillEmptyCellsButton: 'Remplissage automatique des cellules vides',

      bulkAdjustGridValue: 'Ajuster en masse les valeurs du tableau',
      saveZoneLookupTable: 'Enregistrer le tableau de recherche de zones',
      update: 'Mettre à jour',
      save: 'Enregistrer',
      discardChanges: 'Annuler les modifications',
      currentlyNoZonesAdded: "Actuellement, aucune zone n'est ajoutée.",
      addZonesToCreateLookupTable: 'Ajoutez des zones pour créer un tableau de recherche.',
      addZones: 'Ajouter des zones',
      confirmDeleteZoneTable: 'Êtes-vous sûr de vouloir supprimer {{this}}?',
      zoneTableDeletedSuccessfully: 'Table de zones supprimée avec succès',
      confirmDuplicateZoneTable: 'Êtes-vous sûr de vouloir dupliquer cette table de zones ?',
      newZoneTable: 'Nouvelle table de zones',
      duplicateZoneTable: 'Dupliquer la table de zones',
      addZoneTable: 'Ajouter une table de zones',
      noZoneTables: 'Aucune table de zones',
      noZoneTablesFound: 'Aucune table de zones trouvée',
      addTable: 'Ajouter une table',
      failedToLoadZoneTables: 'Échec du chargement des tables de zones',
      zoneLookupTableGrid: 'Tableau de recherche de zones',
      fillEmptyCellsWithAmount:
        'Remplissez toutes les cellules vides de la grille de prix avec ce montant.',
      adjustPopulatedCellsByAmount:
        'Ajustez toutes les cellules remplies de la grille de prix avec ce montant.',
      howToUseZoneTable: 'Comment utiliser la table de zones ?',
      reversePricing: 'Tarification inversée',
      bulkAdjustGridValues: 'Ajustement en masse des valeurs de la grille',
      submit: 'Soumettre',
      amount: 'Montant',
      valueCannotBeFilled: 'La valeur ne peut pas être remplie.',
      allCellsAlreadyFilled: 'Toutes les cellules sont déjà remplies.',
      valueCannotBeAdjusted: 'La valeur ne peut pas être ajustée.',
      allCellsEmpty: 'Toutes les cellules sont vides.',
      enterZoneTableNameAndFillCell:
        'Entrez un nom et remplissez au moins une cellule pour créer la table de zones.',
      pleaseAddModifiersToGroup: 'Veuillez ajouter un ou plusieurs modificateurs au groupe.',
    },
    emptyState: {
      link: 'Ajouter une zone',
    },
    header: {
      title: 'Zone',
    },
    colDefs: {
      id: 'ID',
      name: 'Nom',
      postalCodes: 'Codes postaux',
      action: 'Action',
      comment: 'Commentaire',
    },
    operationalForm: {
      name: 'Nom',
      namePlaceholder: 'Entrez le nom de la zone',
      nameError: 'Le nom est requis',
      postalCodes: 'Codes postaux',
      postalCodesPlaceholder: 'Entrez les codes postaux séparés par des virgules',
      postalCodesError: 'Les codes postaux sont requis',
      comments: 'Commentaires',
      commentsDividerText: 'Commentaire',
      commentsPlaceholder: 'Détails supplémentaires sur cette zone',
      delete: 'Supprimer',
    },
  },
  spinner: {
    loading: 'Chargement...',
  },
  statusFallbackPage: {
    backHome: "Retourner à la page d'accueil",
    fallBackMessage: {
      unauthorizedAccess: "Désolé, vous n'êtes pas autorisé à accéder à cette page.",
      pageNotExist: "Désolé, la page que vous avez visitée n'existe pas.",
      somethingWentWrong: "Désolé, quelque chose s'est mal passé.",
    },
    notFound404: {
      title: 'Page non trouvée',
      description: "Oups ! La page que vous recherchez n'existe pas.",
    },
  },
  priceSetPage: {
    errorWhileChangingModifierConfig:
      "Une erreur s'est produite lors de la mise à jour de la configuration du modificateur",
    header: {
      title: 'Définition des prix',
      search: 'Rechercher des ensembles de prix',
      addPriceSetBtn: 'Ajouter un ensemble de prix',
      addPriceSet: 'Ajouter un ensemble de prix',
      editPriceSet: 'Modifier l’ensemble de prix',
    },
    form: {
      name: 'Nom',
      namePlaceholder: 'VNP 4 heures Frais de carburant',
      serviceLevel: 'Niveau de service',
      serviceNameRequired: 'Le nom du service est requis',
      serviceLevelRequired: 'Le niveau de service est requis',
      serviceLevelPlaceholder: '4 heures express',
      paymentOption: 'Option de paiement',
      paymentOptionPlaceholder: 'Sélectionnez une option de paiement',
      notes: 'Remarques',
      notesPlaceholder: 'Les remarques ajoutées ici seront également visibles par le client.',
      description: 'Description',
      descriptionPlaceHolder:
        'La description ajoutée ici ne sera visible que par les administrateurs.',
      selectZoneTableGridPlaceholder: 'Sélectionnez une table de zones',
      editZoneTableBtn: 'Modifier cette table de zones',
      noServicesAssigned: 'Aucun service attribué',
      toAssignServiceClick: 'Pour attribuer un service, cliquez',
      here: 'ici',
    },
    helpModalStrings: {
      modalTitle:
        'Comment fonctionnent les modificateurs de prix dans l’éditeur d’ensemble de prix',
      modalDescription:
        'L’éditeur d’ensemble de prix vous permet de configurer et d’assigner plusieurs modificateurs de prix pour ajuster les prix de manière dynamique.',
      buttonText: 'Ok, compris',

      addPriceModifiersTitle: 'Ajouter des modificateurs de prix',
      addPriceModifiersDescription: 'Cliquez sur',
      addPriceModifiersStrong: 'Ajouter des modificateurs de prix',
      addPriceModifiersEnd: 'pour choisir des modificateurs prédéfinis ou personnalisés.',

      configureDefaultBehaviorTitle: 'Configurer le comportement par défaut',
      configureDefaultBehaviorDescription: 'Utilisez la',
      configureDefaultBehaviorStrong: 'Configuration par défaut',
      configureDefaultBehaviorEnd: 'pour définir le comportement d’un modificateur.',

      noneLabel: 'Aucun : ',
      noneDescription: 'Le modificateur est désactivé.',

      selectedLabel: 'Sélectionné : ',
      selectedDescription: 'Le modificateur est optionnel mais actif par défaut.',

      requiredLabel: 'Requis : ',
      requiredDescription: 'Le modificateur est obligatoire.',

      exampleTitle: 'Exemple',
      exampleDescription: 'Appliquer un supplément pour les articles de plus de 96 pouces :',
      exampleStep1: 'Ajoutez des calculs de prix basés sur la longueur pour 96 pouces et plus.',
      exampleStep2: 'Définissez-le comme Requis dans le comportement par défaut.',
    },

    tabs: {
      general: 'Général',
      schedule: 'Planning',
      basePriceByZone: 'Prix de base par zone',
      priceModifier: 'Modificateurs de prix',
      customers: 'Clients',
      history: 'Historique',
    },
    colDefs: {
      name: 'Nom',
      service: 'Service',
      configByDefault: 'Configuration par défaut',
    },
    options: {
      none: 'Aucun',
      selected: 'Sélectionné',
      required: 'Requis',
    },
    customers: {
      colDefs: {
        companyName: 'Nom de l’entreprise',
        customerName: 'Nom du client',
      },
      assignCustomersTitle: 'Assigner des clients',
      assignCustomersBtn: 'Assigner des clients',
      assignedCustomers: 'Clients assignés',
      unassignedCustomers: 'Clients non assignés',
      searchPlaceholder: 'Rechercher un client',
      noCustomersFound: 'Aucun client trouvé',
      noCustomersAssigned: 'Aucun client assigné',
      noCustomersAvailable: 'Aucun client disponible',
      noCustomersAvailableToAssign: 'Aucun client disponible à assigner',
      toGetStarted: 'Pour commencer,',
    },
    schedule: {
      availabilityOptions: {
        never: 'Jamais',
        weekly: 'Hebdomadaire',
        always: 'Toujours',
      },
      notification: {
        scheduleSaved: 'Session enregistré avec succès',
      },
      offsetTypeOptions: {
        to: 'Jusqu’à',
        by: 'Par',
      },
      week: {
        includes: 'Inclus les week-ends',
        excludes: 'Exclut les week-ends',
      },
      form: {
        priceSetAvailability: 'Disponibilité de l’ensemble de prix',
        maxScheduleLimitInfoMessage: 'Vous pouvez ajouter un maximum de {{limit}} plannings',
        days: 'Jours',
        startTime: 'Heure de début',
        endTime: 'Heure de fin',
        requiredTime: 'Veuillez sélectionner une heure de planification.',
        selectDaysPlaceholder: 'Sélectionner des jours',
        requiredDays: 'Veuillez sélectionner des jours.',
        addScheduleBtn: 'Ajouter un planning',
        offsetDueDate: 'Décalage de la date d’échéance',
        hours: 'Heure(s)',
        hoursPlaceholder: '00',
        requiredHours: 'Les heures sont requises',
        minutes: 'Minute(s)',
        minutesPlaceholder: '00',
        requiredMinutes: 'Les minutes sont requises',
        daysOut: 'Jours à l’avance',
        daysOutPlaceholder: '00',
        requiredDaysOut: 'Les jours à l’avance sont requis',
        sameTimeError: 'Impossible de définir la même heure de planification',
        minuteLimitError: 'Les minutes ne doivent pas dépasser 59',
      },
    },

    priceMod: {
      searchPlaceholder: 'Rechercher des modificateurs de prix',
      assignPriceModBtn: 'Assigner des modificateurs de prix',
      notFound: 'Aucun modificateur de prix trouvé',
      assignToSet: 'Assigner des modificateurs de prix à cet ensemble de prix.',
      toAssignMod: 'Pour assigner des modificateurs,',
      getStarted: 'Pour commencer',
      clickHear: 'Cliquez ici.',
      addSet: 'Ajouter un modificateur de prix.',
      assignModifierWithBack: 'Assigner des modificateurs de prix',
      unassignMods: 'Modificateurs non assignés',
      assignMods: 'Modificateurs assignés',
      noModsAssigned: 'Aucun modificateur assigné.',
      noModsFoundToAssigned: 'Aucun modificateur disponible à assigner',
      notAvailable: 'Aucun modificateur disponible',
    },
    tooltip: {
      tabRestriction: 'Veuillez ajouter un ensemble de prix pour activer l’accès à cet onglet.',
      notes: 'Les remarques ajoutées ici seront également visibles par le client.',
      description: 'La description ajoutée ici ne sera visible que par les administrateurs.',
      disableZoneTableSaveBtn:
        'Sélectionnez une table de zones dans le menu déroulant ou ajoutez une valeur de cellule pour assigner la table de zones à cet ensemble de prix.',
    },
    alert: {
      duplicateConfirmation: 'Êtes-vous sûr de vouloir dupliquer cet ensemble de prix ?',
      deleteConfirmation: 'Êtes-vous sûr de vouloir supprimer cet ensemble de prix ?',
      overWriteZoneTableConfirmationTitle:
        'Êtes-vous sûr de vouloir écraser la table de zones actuelle ?',
      overWriteZoneTableConfirmationMsg:
        'Cette action remplacera les valeurs existantes par de nouvelles.',
      updateZoneTableConfirmationMsg:
        'Cette action mettra à jour les valeurs existantes avec de nouvelles.',
      deleteConfirmationMessage:
        'Cette action est irréversible. La suppression de cet ensemble de prix entraînera la suppression de tous ses détails et de son historique de vos enregistrements.',
      overWriteZoneTableOnSave:
        'Êtes-vous sûr de vouloir écraser les valeurs existantes de la table des zones ?',
    },
    notification: {
      successAdded: 'Ensemble de prix ajouté avec succès',
      successDelete: 'Ensemble de prix supprimé avec succès',
      successEdit: 'Ensemble de prix mis à jour avec succès',
      successAssignedZoneTable: 'Table de zones assignée avec succès à cet ensemble de prix',
      errorAssignZoneTable: 'Échec de l’assignation de la table de zones à cet ensemble de prix',
    },

    contextMenu: {
      addPriceSet: 'Ajouter un nouvel ensemble de prix',
      duplicatePriceSet: 'Dupliquer l’ensemble de prix',
      assignUnassign: 'Assigner ou désassigner à un client',
    },
    emptyState: {
      title: 'Aucun ensemble de prix trouvé',
      description: 'Veuillez ajouter un nouvel ensemble de prix',
      link: 'Ajouter un ensemble de prix',
    },
  },
  priceModifiers: {
    updateModifier: 'Mettre à jour le modificateur',
    addModifier: 'Ajouter un modificateur',
    addModifierGroup: 'Ajouter un groupe de modificateurs',
    confirmDeletePriceModifier: 'Êtes-vous sûr de vouloir supprimer ce modificateur de prix ?',
    deleteModifierWarning:
      'Cette action ne peut pas être annulée. La suppression de ce modificateur de prix supprimera tous ses détails et son historique de vos enregistrements.',
    priceModifierDeleted: 'Modificateur de prix supprimé avec succès',
    duplicate: 'Dupliquer',
    fixedAmount: 'Montant fixe de 0,00 $\nFormule :',
    fixedAmountLabel: 'Montant fixe',
    fixedPercent: 'Pourcentage fixe',
    fixedOverageAmount: 'Montant fixe de dépassement',
    fixedOveragePercent: 'Pourcentage fixe de dépassement',
    tieredFixedOverageAmount: 'Montant fixe escalonné de dépassement',
    tieredFixedOveragePercent: 'Pourcentage fixe escalonné de dépassement',
    incrementalOverageAmount: 'Montant supplémentaire de dépassement',
    incrementalOveragePercent: 'Pourcentage supplémentaire de dépassement',
    tieredIncrementalOverageAmount: 'Montant supplémentaire escalonné de dépassement',
    tieredIncrementalOveragePercent: 'Pourcentage supplémentaire escalonné de dépassement',
    basePrice: 'Prix de base',
    declaredValue: 'Valeur déclarée',
    cubicDimensions: 'Dimensions cubiques',
    distance: 'Distance',
    height: 'Hauteur',
    width: 'Largeur',
    length: 'Longueur',
    weight: 'Poids',
    quantity: 'Quantité',
    collectionWaitTime: 'Temps d’attente pour la collecte (secondes)',
    deliveryWaitTime: 'Temps d’attente pour la livraison (secondes)',
    priceModifierSaved: 'Modificateur de prix enregistré avec succès',
    priceModifierNotSaved: 'Modificateur de prix non enregistré',
    pricingMethod: 'Méthode de tarification',
    selectPriceMethod: 'Sélectionnez la méthode de tarification',
    initialValue: 'Valeur initiale',
    maximumValueExceeded: 'Valeur maximale dépassée',
    applicableRange: 'Plage applicable',
    valueMustBeGreaterThanApplicableRangeFrom:
      'La valeur doit être supérieure à la plage applicable à partir de',
    maximumPercentExceeded: 'Pourcentage maximal dépassé',
    configureTiers: 'Configurer les niveaux',
    summary: 'Résumé',
    commonTwoHoursSkidPriceCalculation: 'Calcul du prix commun pour palette 2 heures (QTE)',
    calculationBasedOn: 'Calcul basé sur',
    selectBaseField: 'Sélectionner le champ de base',
    to: 'À',
    stepValue: 'Valeur de l’étape',
    summaryDetails: 'Détails du résumé',
    calculationType: 'Type de calcul',
    adjustmentTypeLabel: "de l'ajustement",
    noPriceModifierFound: 'Aucun modificateur de prix trouvé',
    pleaseAddNewPriceModifier: 'Veuillez ajouter un nouveau modificateur de prix',
    addPriceModifier: 'Ajouter un modificateur de prix',
    colDefs: {
      kind: 'Type',
      name: 'Nom',
    },
    configureTiersForm: {
      add: 'Ajouter',
      startValueIsRequired: 'La valeur de départ est requise',
      pleaseEnterValidNumber: 'Veuillez entrer un nombre valide',
      endValueIsRequired: 'La valeur de fin est requise',
      endValueShouldBeGreaterThanStartValue:
        'La valeur de fin doit être supérieure à la valeur de départ',
      end: 'Fin',
      start: 'Début',
      amountIsRequired: '{{Amount}} est requis',
      adjustmentAmountIsRequired: 'Amount est requis',

      defaultAmountIfNoMatchingTiers:
        'Valeur par défaut {{amount}} s’il n’y a pas de niveaux correspondants:',
      amount: 'Amount',
      percentage: 'Percentage',
      basePriceTiers: 'Niveaux de prix de base',
    },
    groupModifiers: {
      behavior: 'Comportement',
      addPriceModifiersGroup: 'Ajouter un groupe de modificateurs de prix',
      vnpFourHoursFuelCharges: 'Frais de carburant VNP 4 heures',
      selectBehavior: 'Sélectionner un comportement',
      descriptionVisibleToAdminsOnly:
        'La description ajoutée ici sera uniquement visible par les administrateurs.',
      unassignedModifiers: 'Modificateurs non attribués',
      noModifiersAvailable: 'Aucun modificateur disponible.',
      assignedModifiersToGroupList: 'Modificateurs assignés à la liste du groupe',
      searchModifiers: 'Rechercher des modificateurs',
      noModifiersAssigned: 'Aucun modificateur assigné.',
      priceModifierGroup: 'Groupe de modificateurs de prix',
      priceModifier: 'Modificateur de prix',
      modifiersGroupList: 'Liste des groupes de modificateurs',
      assignModifiers: 'Attribuer des modificateurs',
      noSavedGroupModifiersFound: 'Aucun groupe de modificateurs enregistré trouvé',
      groupModifierCreatedSuccessfully: 'Groupe de modificateurs créé avec succès',
      groupModifierUpdatedSuccessfully: 'Groupe de modificateurs mis à jour avec succès',
      updatePriceModifiersGroup: 'Mettre à jour le groupe de modificateurs de prix',
      useHighestPricedModifier: 'Utiliser le modificateur de prix le plus élevé',
      useLowestPricedModifier: 'Utiliser le modificateur de prix le plus bas',
      useSumOfAllModifiers: 'Utiliser la somme de tous les modificateurs',
    },
    formulaDescription: {
      forFlatRate: 'Ajouter des frais fixes de ${{rate}} à toutes les livraisons.',
      forFlatPercent:
        'Ajouter une surcharge de {{percent}}% sur le {{calculationBasedOn}} de la commande.',
      surcharge: 'Surcharge',
      exceeds: 'Dépasse',
      isGreaterThan: 'Est supérieur à',
      isMoreThan: 'Est plus que',
      greaterThan: 'Supérieur à',
      greaterThanOrEqual: 'Supérieur ou égal à',
      lessThan: 'Inférieur à',
      lessThanOrEqual: 'Inférieur ou égal à',
      ifThe: 'Si le',
      addA: 'Ajouter un',
      from: 'De',
      to: 'À',
      pleaseSelectBehavior: 'Veuillez sélectionner le comportement',
      pricingMethodRequired: 'La méthode de tarification est requise',
      calculationFieldRequired: 'Le champ de calcul est requis',
      initialValueRequired: 'La valeur initiale est requise',
      minApplicableRangeRequired: 'La plage minimale applicable est requise',
      maxApplicableRangeRequired: 'La plage maximale applicable est requise',
      stepValueRequired: 'La valeur du pas est requise',
      adjustmentTypeRequired: 'Le type d’ajustement est requis',
    },
  },
  systemErrors: {
    unknown: 'Erreur système générique inconnue',
    validationError: 'Erreur de validation',
    configurationError: 'Erreur de configuration du système',
    unknownException: 'Une erreur est survenue, veuillez rafraîchir la page et réessayer.',
    whileAssigningServices:
      'Une erreur s’est produite lors de l’attribution des ensembles de prix au client',

    authErrors: {
      authenticationRequired: 'Authentification requise',
      insufficientPermissions: 'Permissions insuffisantes',
      invalidCredentials: 'Identifiants fournis invalides',
      tokenExpired: 'Session expired. please login again.',
      tokenInvalid: 'Jeton d’authentification invalide',
      tokenMissing: 'Jeton d’authentification manquant',
    },
    dataErrors: {
      resourceNotFound: 'Ressource non trouvée',
      duplicateEntry: 'Entrée en double',
      invalidDataFormat: 'Format de données invalide',
      dataConstraintViolation: 'Violation de contrainte de données',
    },
    businessErrors: {
      setting: {
        notFound: 'Paramètres introuvables',
      },
      tenant: {
        notFound: 'Locataire non trouvé',
        alreadyExists: 'Le locataire existe déjà',
        inactive: 'Le locataire est inactif',
        deleted: 'Le locataire est supprimé',
        invalidStatus: 'Statut du locataire invalide',
        operationNotAllowed: 'Opération non autorisée sur le locataire',
        uniqueConstraintViolation: 'Violation de contrainte unique pour le locataire',
      },
      user: {
        notFound: 'Utilisateur non trouvé',
        alreadyExists: 'L’utilisateur existe déjà',
        inactive: 'L’utilisateur est inactif',
        deleted: 'L’utilisateur est supprimé',
        emailExists: 'L’email existe déjà',
        invalidStatus: 'Statut de l’utilisateur invalide',
        operationNotAllowed: 'Opération utilisateur non autorisée',
        credentialsExpired: 'Les identifiants de l’utilisateur ont expiré',
      },
      vehicle: {
        notFound: 'Véhicule non trouvé',
        alreadyExists: 'Le véhicule existe déjà',
        inactive: 'Le véhicule est inactif',
        deleted: 'Le véhicule est supprimé',
        invalidStatus: 'Statut du véhicule invalide',
        operationNotAllowed: 'Opération sur le véhicule non autorisée',
        maintenanceOverdue: 'Entretien du véhicule en retard',
      },
      vehicleType: {
        notFound: 'Type de véhicule non trouvé',
        alreadyExists: 'Le type de véhicule existe déjà',
        inactive: 'Le type de véhicule est inactif',
        deleted: 'Le type de véhicule est supprimé',
        invalidStatus: 'Statut du type de véhicule invalide',
        operationNotAllowed: 'Opération sur le type de véhicule non autorisée',
        inUse: 'Type de véhicule en cours d’utilisation',
      },
      priceModifiers: {
        priceModifierExists: 'Le modificateur de prix avec ce nom existe déjà pour ce locataire',
        cannotDeletePriceModifier:
          'Impossible de supprimer ce modificateur de prix car il est déjà utilisé dans un autre groupe de modificateurs',
        groupModifierDeletedSuccessfully: 'Groupe de modificateurs supprimé avec succès',
        groupModifierExists: 'Un groupe de modificateurs avec ce nom existe déjà',
        cannotDeleteGroupModifier:
          'Impossible de supprimer ce groupe de modificateurs car il est utilisé dans un autre groupe de modificateurs',
      },
      zone: {
        zoneNameExists: 'Une zone avec ce nom existe déjà.',
      },
      zoneTable: {
        zoneTableExists: 'Une zone table avec ce nom existe déjà.',
      },
      order: {
        orderNotFound: 'Commande introuvable',
        cannotTransitionFromDraftToCompleted:
          "Impossible de passer la commande de l'état {{currentStatus}} à {{newStatus}}",
        orderNotAssignedToDriver: "La commande n'est attribuée à aucun chauffeur",
        orderUpdateFailed: 'Échec de la mise à jour de la commande',
        orderAlreadyAssigned: 'La commande a déjà été attribuée',
        orderDeliveryLocationNotFound: 'Lieu de livraison de la commande introuvable',
        orderPickupLocationNotFound: 'Lieu de ramassage de la commande introuvable',
        orderCustomerSignatureRequired: 'La signature du client est requise pour cette commande',
        orderLocked: 'Cette commande est verrouillée et ne peut pas être modifiée',
        orderCreationFailed: 'Échec de la création de la commande',
        orderItemNotFound: 'Élément de la commande introuvable',
        orderCompletionRequirement:
          'Les conditions de finalisation de la commande ne sont pas remplies',
        orderDeliveryTimeInvalid: 'Heure de livraison spécifiée invalide pour la commande',
      },
    },
    externalServiceErrors: {
      generic: 'Erreur générique du service externe',
      httpRequestFailed: 'Échec de la requête HTTP',
      databaseError: 'Échec de l’opération de base de données',
      fileUploadFailed: 'Échec du téléversement du fichier',
      fileNotFound: 'Fichier non trouvé',
      emailSendFailed: 'Échec de l’envoi de l’email',
    },
    infrastructureErrors: {
      generic: 'Erreur d’infrastructure générique',
      networkError: 'Erreur de communication réseau',
      storageError: 'Erreur du système de stockage',
      cacheError: 'Erreur du système de cache',
      queueError: 'Erreur de la file d’attente de messages',
    },
  },
  quickFilter: {
    quickFilterAddedSuccessfully: 'Filtre rapide ajouté avec succès',
    quickFilterDeletedSuccessfully: 'Filtre rapide supprimé avec succès',
    quickFilterLabel: 'Filtre rapide',
    emptyFieldError: 'Veuillez remplir correctement les champs',
    quickFilterExists: 'Un filtre rapide avec ce nom existe déjà',
  },
  fallBackPages: {
    unauthorized: "Désolé, vous n'êtes pas autorisé à accéder à cette page.",
    pageNotExist: "Désolé, la page que vous avez visitée n'existe pas.",
    unknown: "Désolé, une erreur s'est produite.",
  },
  ordersPage: {
    noOrdersFound: 'Aucune commande trouvée',
    appliedFilter: 'Filtre appliqué',
    searchOrders: 'Rechercher des commandes',
    driver: 'Chauffeur',
    selectDriverToAssign: 'Sélectionnez un chauffeur à assigner',
    assign: 'Attribuer',
    collectionCompanyName: "Nom de l'entreprise de collecte",
    deliveryCompanyName: "Nom de l'entreprise de livraison",
    status: 'Statut',
    unassigned: 'Non assigné',
    confirm: 'Confirmer',
    orderSuccessfullyDeleted: 'Commande supprimée avec succès',
    orderStatusSuccessfullyUpdated: 'Statut de la commande mis à jour avec succès',
    failedToDeleteOrder: 'Échec de la suppression de la commande',
    orderNotFoundTryAgain: 'Commande introuvable, veuillez réessayer',
    updatedFromContextMenu: 'Mis à jour depuis le menu contextuel',
    submitted: 'Soumise',
    inTransit: 'En transit',
    delivered: 'Livrée',
    deleteOrderConfirmationTitle: 'Êtes-vous sûr de vouloir supprimer cette commande ?',
    deleteOrderConfirmationMessage:
      'La suppression de cette commande supprimera tous ses détails et son historique de vos dossiers.',
    updateOrderStatusTitle: 'Mettre à jour le statut de la commande',
    updateOrderStatusMessage:
      'Êtes-vous sûr de vouloir changer le statut de la commande en {{status}} ?',
    setToSubmitted: 'Définir comme soumise',
    statusValues: {
      draft: 'Brouillon',
      pendingApproval: "En attente d'approbation",
      ready: 'Prêt',
      cancelled: 'Annulé',
      rejected: 'Rejeté',
      onHold: 'En attente',
      cancelledBilled: 'Annulé facturé',
      deleted: 'Supprimé',
      refunded: 'Remboursé',
      inTransit: 'En transit',
      pending: 'En attente',
      assigned: 'Assigné',
      inProgress: 'En cours',
      completed: 'Terminé',
    },
    noQuickFiltersAvailable: 'Aucun filtre rapide disponible',
    deleteQuickFilter: 'Supprimer le filtre rapide',
    areYouSureDeleteQuickFilter: 'Êtes-vous sûr de vouloir supprimer ce filtre rapide ?',
    quickFilter: 'Filtre rapide',
    deliveryTime: 'Heure de livraison',
    deliveryCompany: 'Entreprise de livraison',
    collectionTime: 'Heure de collecte',
    assignee: 'Assigné à',
    setToInTransit: 'Mettre en transit',
    setToDelivered: 'Marquer comme livré',
    setToPickup: 'Marquer pour enlèvement',
    updateStatus: 'Mettre à jour le statut',
    unassign: 'Désattribuer',
    sendNotification: 'Envoyer une notification',
    sendStatusToCustomer: 'Envoyer le statut au client',
    sendStatusToReceiver: 'Envoyer le statut au destinataire',
    sendStatusToCollector: 'Envoyer le statut au collecteur',
    sendStatusToDriver: 'Envoyer le statut au chauffeur',
    selectDriver: 'Sélectionner un chauffeur',
    dateSubmitted: 'Date soumise',
    collectionCompany: 'Entreprise de collecte',
    trackingNumber: 'Numéro de suivi',
    missingField: 'Champ manquant',
    pricesBreakdown: 'Détail des prix',
    packages: 'Colis',
    attachments: 'Pièces jointes',
    viewOrder: 'Voir la commande',
    collectionLocation: 'Lieu de collecte',
    enterCollectionLocationDetails: 'Entrez les détails du lieu de collecte',
    deliveryLocation: 'Lieu de livraison',
    enterDeliveryLocationDetails: 'Entrez les détails du lieu de livraison',
    orderDetails: 'Détails de la commande',
    orderInfoDataFields: {
      trackingNo: 'N° de suivi',
      dateSubmitted: 'Date de soumission',
      numberOfPackages: 'Nbre de colis',
      pickupDateTime: 'Date et heure de ramassage',
      pickupSignatureRequired: 'Signature requise (Ramassage)',
      deliveryByDate: 'Date de livraison',
      cod: 'Paiement à la livraison (COD)',
      deliverySignatureRequired: 'Signature requise (Livraison)',
      vehicle: 'Véhicule',
      codAmount: 'Montant COD',
      description: 'Description',
      driverName: 'Nom du chauffeur',
      status: 'Statut',
      notes: 'Remarques',
    },
    orderPriceDataFields: {
      serviceLevel: 'Niveau de service',
      priceSet: 'Ensemble de prix',
      basePrice: 'Prix de base',
      optionsPrice: 'Prix des options',
      total: 'Total',
      miscAdjustment: 'Ajustement divers',
      insurance: 'Assurance',
      billingStatus: 'Statut de facturation',
      totalDeclaredValue: 'Valeur déclarée totale',
      invoiceNumber: 'Numéro de facture',
      paymentStatus: 'Statut du paiement',
    },
    statisticInfoDataFields: {
      distanceKm: 'Distance',
      totalQuantity: 'Quantité totale',
      combinedWeightKg: 'Poids combiné (Kg)',
      deliveryWaitTime: "Temps d'attente pour la livraison",
      pickupWaitTime: "Temps d'attente pour l'enlèvement",
    },
    customerInfoDataFields: {
      company: 'Entreprise',
      customer: 'Client',
      phone: 'Téléphone',
      email: 'E-mail',
      contact: 'Contact',
    },
    collectAt: 'Collecter à',
    amountToCollect: 'Montant à encaisser',
    orderDetailsChange: 'Changement des détails de la commande',
    priceBreakDown: {
      idNotFound: 'ID du modificateur introuvable',
      colDefs: {
        appliedModifier: 'Modificateurs appliqués',
        appliedCharges: 'Frais appliqués',
        description: 'Description',
      },
      modifierPricingButton: 'Modifier le prix',
      mustBeMoreThan1: 'Le montant doit être supérieur à 1',
      customPricingModelTitle: 'Prix personnalisé',
      nameLabel: 'Nom',
      amountLabel: 'Montant',
      requiredName: 'Le nom est requis',
      requiredAmount: 'Le montant est requis',
      namePlaceholder: 'Saisissez le nom',
      negativeOrPositiveValue: 'Le montant doit être une valeur négative ou positive.',
      customPricingModelTitleEditMode: 'Voir le modificateur de prix',
      customPricingModelDescEditMode: 'Détails du modificateur appliqué à cette commande',
      customPricingModelDesc:
        'Utilisez cette option pour ajouter un prix personnalisé ou accorder une remise pour une commande particulière',
      emptyState: {
        title: 'Aucun modificateur trouvé',
        description: 'Aucun modificateur de prix inclus dans cet ensemble de prix',
      },
    },
    packagesTab: {
      addPackage: 'Ajouter un colis',
      viewPackage: 'Voir le colis',
      addPackageDesc: 'Remplissez les détails pour ajouter un nouveau colis.',
      mustBeMoreThan1: 'La valeur doit être supérieure à 1',
      viewPackageDesc: 'Modifier ou visualiser le colis ci-dessous',
      placeholderPackageType: 'Sélectionnez le type de colis',
      requiredPackageType: 'Veuillez sélectionner un type de colis',
      requiredQuantity: 'La quantité est requise',
      requiredLength: 'La longueur est requise',
      requiredWidth: 'La largeur est requise',
      requiredHeight: 'La hauteur est requise',
      requiredWeight: 'Le poids total est requis',
      FormLabels: {
        packageType: 'Type de colis',
        quantity: 'Quantité',
        CombinedWeight: 'Poids total',
        length: 'Longueur',
        width: 'Largeur',
        height: 'Hauteur',
        CubicDimension: 'Dimension cubique',
        image: 'Télécharger une image',
      },
      placeholders: {
        defaultValue: '0',
      },
      tooltips: {
        uploadImage: 'Téléchargez une image claire du colis. Taille maximale du fichier : 5 Mo.',
      },
      notifications: {
        packageAddedSuccess: 'Colis ajouté avec succès',
        packageUpdatedSuccess: 'Colis mis à jour avec succès',
      },
      notAvailable: 'N/A',
      actions: {
        view: 'voir',
      },
      colDefs: {
        packageType: 'Type de colis',
        quantity: 'Quantité',
        CombinedWeight: 'Poids total',
        CubicDimension: 'Dimension',
        image: 'Image',
      },
      uploadImage: 'Téléverser une image',
      chooseImage: 'Choisir une image',
      uploadButtonText: 'Téléverser',
    },
    attachmentsTab: {
      uploadFiles: 'Téléverser des fichiers',
      uploadType: 'Type de téléversement',
      uploadLabel: 'Téléverser un fichier ou une image',
      requiredUploadType: 'Veuillez sélectionner un type de téléversement',
      requiredAttachmentOrFile: 'Veuillez choisir une pièce jointe ou un fichier',
      selectPlaceholder: 'Sélectionnez le type de téléversement',
      attachmentFile: 'Fichier joint',
      type: 'Type',
      dateAdded: "Date d'ajout",
      uploadTypeOptions: {
        collectionSignature: 'Signature de collecte',
        deliverySignature: 'Signature de livraison',
        fileAttachment: 'Fichier joint',
      },
      deleteConfirmationMessage:
        'Cette action est irréversible. Supprimer cette pièce jointe supprimera toutes ses informations de vos dossiers.',
      uploadAttachment: 'Téléverser un fichier',
      attachmentDeletedSuccessfully: 'Pièce jointe supprimée avec succès',
      attachmentAddedSuccessfully: 'Pièce jointe ajoutée avec succès',
      failedToAddAttachment: "Échec de l'ajout de la pièce jointe",
      failedToDownloadItem: "Échec du téléchargement de l'élément",
      downloadTitle: 'télécharger',
      deleteTitle: 'supprimer',
      fileSizeValidation: 'La taille du fichier doit être inférieure à 5 Mo',
      noFilesOrAttachmentsFound: 'Aucun fichier ou pièce jointe trouvé.',
    },
  },
};

export default FR;
