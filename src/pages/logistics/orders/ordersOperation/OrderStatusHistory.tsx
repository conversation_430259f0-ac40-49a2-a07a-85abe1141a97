import { Steps } from 'antd';
import React, { useMemo } from 'react';
import dayjs from 'dayjs';

export enum OrderStatus {
  Draft = 0,
  Submitted = 1,
  Assigned = 2,
  InTransit = 3,
  Completed = 4,
}

const statusMeta: Record<keyof typeof OrderStatus, { title: string; defaultDescription: string }> = {
  Draft: { title: 'Draft', defaultDescription: 'Order is being created.' },
  Submitted: { title: 'Submitted', defaultDescription: 'Order submitted.' },
  Assigned: { title: 'Assigned', defaultDescription: 'Driver assigned.' },
  InTransit: { title: 'In transit', defaultDescription: 'Out for delivery.' },
  Completed: { title: 'Completed', defaultDescription: 'Delivered.' },
};

interface StatusHistoryItem {
  newStatus: keyof typeof OrderStatus;
  changedAt: string;
  comments?: string | null;
}

interface Props {
  orderDetails: {
    statusHistory?: StatusHistoryItem[];
  };
}

const OrderStatusSteps: React.FC<Props> = ({ orderDetails }) => {

  if (!orderDetails) return null;

  const stepsData = useMemo(() => {
    const historyMap = new Map<string, StatusHistoryItem>();

    (orderDetails.statusHistory || []).forEach((item) => {
      historyMap.set(item.newStatus, item);
    });

    const draftPresent = orderDetails.statusHistory?.some(
      (item) => item.newStatus === 'Draft'
    );

    const fullStatusOrder: (keyof typeof OrderStatus)[] = draftPresent
      ? ['Draft', 'Submitted', 'Assigned', 'InTransit', 'Completed']
      : ['Submitted', 'Assigned', 'InTransit', 'Completed'];

    const items = fullStatusOrder.map((statusKey) => {
      const historyItem = historyMap.get(statusKey);
      const meta = statusMeta[statusKey];

      return {
        title: meta.title,
        description: (
          <div className="flex flex-col w-full text-primary-300">
            <span className="text-[12px]">
              {historyItem?.comments || meta.defaultDescription}
            </span>
            {historyItem?.changedAt && (
              <span className="text-[12px]">
                {dayjs(historyItem.changedAt).format('DD/MM/YYYY hh:mm A')}
              </span>
            )}
          </div>
        ),
        isDone: !!historyItem,
      };
    });

    const currentStep = items.findIndex((step) => !step.isDone);

    return {
      steps: items,
      current: currentStep === -1 ? items.length - 1 : currentStep,
    };
  }, [orderDetails.statusHistory]);

  return (
    <div className="border-[1px] border-[#BFC2DF] rounded-[8px] p-2 sm:p-4">
      <Steps
        size="small"
        current={stepsData.current}
        items={stepsData.steps.map(({ title, description }) => ({
          title,
          description,
        }))}
        className="orders-stepper !border-none"
      />
    </div>
  );
};

export default OrderStatusSteps;

