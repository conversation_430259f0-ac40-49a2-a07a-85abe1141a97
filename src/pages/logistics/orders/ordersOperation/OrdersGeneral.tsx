import {
  CollapseDownIcon,
  CollapseUpIcon,
  DateCalendarIcon,
  EditPopupIcon,
  infoCircleOutlined,
  LocationOutlinedIcon,
  RightArrowIcon,
} from '@/assets';
import {
  Button,
  Card,
  Collapse,
  DatePicker,
  Form,
  Input,
  InputNumber,
  InputRef,
  Select,
  Space,
  Switch,
  TimePicker,
} from 'antd';
import '../orders.css';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import CustomDivider from '@/components/common/divider/CustomDivider';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { formErrorRegex } from '@/constant/Regex';
import {
  validateCountryAndValue,
  validateMaskedInput,
  numberFieldValidator,
} from '@/lib/FormValidators';
import { Autocomplete } from '@react-google-maps/api';
import MaskedInput from 'antd-mask-input';
import TextArea from 'antd/es/input/TextArea';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { AxiosError } from 'axios';
import { TrackedError } from '@/types/AxiosTypes';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { zoneService } from '@/api/zones/useZones';
import { LockIcon, OrdersWarningIcon } from '@/assets';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { isFormChangedHandler } from '@/lib/helper';
import usePreventExits from '@/hooks/usePreventExits';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { orderServiceHook } from '@/api/orders/useOrders';
import {
  CreateOrdersDto,
  IResponseOrderDto,
  IUpdateOrderStatusPayload,
} from '@/api/orders/order.types';
import { convertInUtcToDayjs, dateFormatter } from '@/lib/helper/dateHelper';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { orderStatusOptions } from '@/constant/generalConstant';
import { useGetDrivers } from '@/api/driver/driver.service';
import { IDrivers } from '@/api/driver/driver.types';
import { OrderStatusEnums } from '@/types/enums/orderStatus';
import OrderStatusSteps from './OrderStatusHistory';
import { orderManagementHooks } from '@/api/orderManagment/useOrderManagement';
import useThrottle from '@/hooks/useThrottle';
import CustomGoogleAutoComplete from '@/components/common/customGoogleAutoComplete/CustomGoogleAutoComplete';
import { vehicleServiceHooks } from '@/api/vehicle/useVehicle';

interface IFieldsInput {
  isOpenModal: boolean;
  isOpenTitle: string;
  value: string | any;
  type: string;
  fieldName?: string;
  name: string;
  options?: any[];
  onFinish?: (values?: any) => Promise<void>;
}
const OrdersGeneralComponent = () => {
  const [openPanels, setOpenPanels] = useState(['1']);
  const [isOpen, setIsOpen] = useState({
    isOpenModal: false,
    isOpenTitle: '',
    isOpenDescription: '',
  });
  const [isFieldModalOpen, setIsFieldModalOpen] = useState<IFieldsInput>({
    isOpenModal: false,
    isOpenTitle: '',
    value: '',
    type: '',
    fieldName: '',
    name: '',
    onFinish: undefined,
  });
  const { id } = useParams();
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const [fieldsForm] = Form.useForm();
  const inputPhoneRef = useRef<InputRef>(null);
  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();
  const autocompleteRef = useRef<Autocomplete>(null);
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);

  const {
    data: orderDetails,
    // isFetching,
    // isPending,
    refetch: refetchOrderDetails,
  } = orderServiceHook.useEntity<IResponseOrderDto>(id as string, {
    enabled: Boolean(id),
    retry: 0,
  });

  const { data: drivers } = useGetDrivers();
  const { data: vehicles } = vehicleServiceHooks.useEntities('minimal');

  const vehiclesOptions = useMemo(() => {
    return vehicles?.data?.map((vehicle: any) => ({
      value: vehicle?.id,
      label: `${vehicle?.model} - ${vehicle?.make}(${vehicle?.vehicleType})`,
    }));
  }, [vehicles]);

  const driversOptions =
    useMemo(() => {
      return drivers?.map((driver: IDrivers) => ({ value: driver.id, label: driver.name }));
    }, [drivers]) || [];

  const notificationManager = useNotificationManager();

  const updateStatusMutation = orderServiceHook.usePatch<IUpdateOrderStatusPayload>();
  const updateOrderMutation = orderServiceHook.useUpdate<Partial<CreateOrdersDto>>();

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    if (focus) {
      inputPhoneRef?.current?.focus();
    }
  }, []);

  const InitialValue = useMemo(
    () => (isOpen.isOpenModal ? form.getFieldsValue(true) : {}),
    [form, isOpen.isOpenModal]
  );

  const [toggles, setToggles] = useState({
    isCod: false,
    isInsurance: false,
    signatureRequiredForPickup: false,
    signatureRequiredForDelivery: false,
  });

  interface IUpdateOrderParams {
    payload: Partial<CreateOrdersDto>;
    showNotification: boolean;
  }

  const updateOrderHandler = useThrottle(
    async ({ payload, showNotification = true }: IUpdateOrderParams) => {
      try {
        if (isFieldModalOpen.onFinish) {
          await isFieldModalOpen.onFinish(payload);
        } else {
          await updateOrderMutation.mutateAsync({
            id: orderDetails?.id as string,
            data: payload,
          });
        }

        if (!isFieldModalOpen.onFinish && showNotification) {
          notificationManager.success({
            message: t('common.success'),
            description: 'Order updated successfully',
          });
        }

        setIsFieldModalOpen({
          isOpenModal: false,
          isOpenTitle: '',
          value: '',
          type: '',
          fieldName: '',
          name: '',
          onFinish: undefined,
        });

        await refetchOrderDetails();
      } catch {
        // error will handle by global Notification
      }
    },
    3000
  );

  const canAssignDriver = useMemo(() => {
    return (
      orderDetails?.status !== OrderStatusEnums.DRAFT &&
      orderDetails?.status !== OrderStatusEnums.CANCELLED &&
      orderDetails?.status !== OrderStatusEnums.COMPLETED &&
      orderDetails?.status !== OrderStatusEnums.IN_TRANSIT
    );
  }, [orderDetails?.status]);

  useEffect(() => {
    if (orderDetails) {
      fieldsForm.setFieldsValue({
        ...orderDetails,
        createdAt: convertInUtcToDayjs(orderDetails?.createdAt),
        scheduledCollectionTime: convertInUtcToDayjs(orderDetails?.scheduledCollectionTime),
        scheduledDeliveryTime: convertInUtcToDayjs(orderDetails?.scheduledDeliveryTime),
      });
      setToggles({
        isCod: orderDetails?.codCollected || false,
        isInsurance: orderDetails?.isInsurance || false,
        signatureRequiredForPickup: orderDetails?.collectionSignatureRequired || false,
        signatureRequiredForDelivery: orderDetails?.deliverySignatureRequired || false,
      });
    }
  }, [fieldsForm, orderDetails]);

  const assignOrderMutation = orderManagementHooks.useAssignOrderToDriver();

  const assignDriverHandler = async (value: { assignedDriverId: string }) => {
    await assignOrderMutation.mutateAsync({
      orderId: id as string,
      driverId: value.assignedDriverId,
    });
  };

  const changeOrderStatusHandler = async ({ status }: { status: OrderStatusEnums }) => {
    await updateStatusMutation.mutateAsync({
      id: `${id}/status`,
      data: {
        status,
        reason: 'N/A',
        comments: 'changed from admin panel',
      },
    });
  };

  const { setPreventExit } = usePreventExits();
  const { isBlocked, setIsBlocked } = useNavigationContext();
  const navigate = useNavigate();
  const orderInfoData = {
    Column: [
      {
        key: 'trackingNo',
        label: t('ordersPage.orderInfoDataFields.trackingNo'),
        value: (
          <span className="flex gap-3 w-max">
            {orderDetails?.isLocked && <img src={LockIcon} />}
            {orderDetails?.trackingNumber}
          </span>
        ),
      },
      {
        key: 'dateSubmitted',
        label: t('ordersPage.orderInfoDataFields.dateSubmitted'),
        value: orderDetails?.createdAt ? dateFormatter(orderDetails?.createdAt) : 'N/A',
        editable: true,
        children: (
          <Button
            onClick={() =>
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.dateSubmitted'),
                value: orderDetails?.createdAt
                  ? convertInUtcToDayjs(orderDetails?.createdAt)
                  : undefined,
                type: 'date',
                fieldName: 'Date',
                name: 'createdAt',
              })
            }
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'packages',
        label: t('ordersPage.orderInfoDataFields.numberOfPackages'),
        value: orderDetails?.items?.length || 'N/A',
        children: (
          <img
            className="px-1 py-[8px] cursor-pointer"
            src={RightArrowIcon}
            onClick={() =>
              navigate(
                ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', id as string).replace(
                  ':tab',
                  'packages'
                )
              )
            }
          />
        ),
      },
      {
        key: 'pickupDate&Time',
        label: t('ordersPage.orderInfoDataFields.pickupDateTime'),
        value: orderDetails?.scheduledCollectionTime
          ? dateFormatter(orderDetails?.scheduledCollectionTime)
          : 'N/A',
        editable: true,
        children: (
          <Button
            onClick={() =>
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: 'Pickup date & time',
                value: orderDetails?.scheduledCollectionTime
                  ? convertInUtcToDayjs(orderDetails?.scheduledCollectionTime)
                  : undefined,
                type: 'date',
                name: 'scheduledCollectionTime',
              })
            }
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'signature',
        label: t('ordersPage.orderInfoDataFields.pickupSignatureRequired'),
        value: toggles?.signatureRequiredForPickup ? 'Yes' : 'No',
        children: (
          <Switch
            className="order-collapse-switch"
            disabled={updateOrderMutation.isPending}
            value={toggles?.signatureRequiredForPickup || false}
            onChange={(value) => {
              updateOrderHandler({
                payload: { collectionSignatureRequired: value },
                showNotification: false,
              });
            }}
          />
        ),
      },
      {
        key: 'dateDeliveryBy',
        label: t('ordersPage.orderInfoDataFields.deliveryByDate'),
        value: orderDetails?.scheduledDeliveryTime
          ? dateFormatter(orderDetails?.scheduledDeliveryTime)
          : 'N/A',
        editable: true,
        children: (
          <Button
            onClick={() =>
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.deliveryByDate'),
                value: orderDetails?.scheduledDeliveryTime
                  ? convertInUtcToDayjs(orderDetails?.scheduledDeliveryTime)
                  : undefined,
                type: 'date',
                name: 'scheduledDeliveryTime',
              })
            }
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'cod',
        label: t('ordersPage.orderInfoDataFields.cod'),
        value: toggles.isCod ? 'Yes' : 'No',
        children: (
          <Switch
            className="order-collapse-switch"
            value={toggles?.isCod || false}
            onChange={(value) =>
              updateOrderHandler({ payload: { codCollected: value }, showNotification: false })
            }
          />
        ),
      },
      {
        key: 'signatureRequired',
        label: t('ordersPage.orderInfoDataFields.deliverySignatureRequired'),
        value: toggles?.signatureRequiredForDelivery ? 'Yes' : 'No',
        type: 'toggle',
        children: (
          <Switch
            className="order-collapse-switch"
            value={toggles?.signatureRequiredForDelivery || false}
            onChange={(value) =>
              updateOrderHandler({
                payload: { deliverySignatureRequired: value },
                showNotification: false,
              })
            }
          />
        ),
      },
      {
        key: 'vehicle',
        label: t('ordersPage.orderInfoDataFields.vehicle'),
        value: orderDetails?.assignedVehicleName || 'N/A',
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.vehicle'),
                value: orderDetails?.assignedVehicleId,
                type: 'dropdown',
                name: 'assignedVehicleId',
                options: vehiclesOptions,
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'codAmount',
        label: t('ordersPage.orderInfoDataFields.codAmount'),
        value: orderDetails?.codAmount || 'N/A',
        editable: true,
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: 'COD',
                value: orderDetails?.codAmount || 'N/A',
                type: 'isCod',
                fieldName: '',
                name: 'codAmount',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'description',
        label: t('ordersPage.orderInfoDataFields.description'),
        value: orderDetails?.description || 'N/A',
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.description'),
                value: orderDetails?.description,
                type: 'textarea',
                name: 'description',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'assignedDriverId',
        label: t('ordersPage.orderInfoDataFields.driverName'),
        value: orderDetails?.assignedDriverName || 'N/A',
        editable: true,
        children: canAssignDriver && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.driverName'),
                value: orderDetails?.assignedDriverName || undefined,
                type: 'dropdown',
                name: 'assignedDriverId',
                options: driversOptions,
                onFinish: assignDriverHandler,
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'status',
        label: t('ordersPage.orderInfoDataFields.status'),
        value: orderDetails?.status || 'N/A',
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.status'),
                value: orderDetails?.status || 'N/A',
                type: 'dropdown',
                name: 'status',
                options: orderStatusOptions,
                onFinish: changeOrderStatusHandler,
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'notes',
        label: t('ordersPage.orderInfoDataFields.notes'),
        value: orderDetails?.internalNotes || 'N/A',
        editable: true,
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.notes'),
                value: orderDetails?.internalNotes || 'N/A',
                type: 'textarea',
                name: 'internalNotes',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
    ],
  };
  const orderPriceData = {
    Column: [
      {
        key: 'serviceLevel',
        label: t('ordersPage.orderPriceDataFields.serviceLevel'),
        value: orderDetails?.serviceLevel || 'N/A',
      },
      {
        key: 'priceSet',
        label: t('ordersPage.orderPriceDataFields.priceSet'),
        value: orderDetails?.priceSet || 'N/A',
      },
      {
        key: 'basePrice',
        label: t('ordersPage.orderPriceDataFields.basePrice'),
        value: `$${orderDetails?.basePrice}` || 'N/A',
      },
      {
        key: 'optionsPrice',
        label: t('ordersPage.orderPriceDataFields.optionsPrice'),
        value: `$${orderDetails?.optionsPrice}` || 'N/A',
      },
      {
        key: 'total',
        label: t('ordersPage.orderPriceDataFields.total'),
        value: `$${orderDetails?.totalPrice}` || 'N/A',
      },
      {
        key: 'miscAdjustment',
        label: t('ordersPage.orderPriceDataFields.miscAdjustment'),
        value: `$${orderDetails?.miscAdjustment}` || 'N/A',
        // children: (
        //   <Button
        //     onClick={() => {
        //       setIsFieldModalOpen({
        //         isOpenModal: true,
        //         isOpenTitle: t('ordersPage.orderPriceDataFields.miscAdjustment'),
        //         value: orderDetails?.miscAdjustment,
        //         type: 'number',
        //         name: 'miscAdjustment',
        //       });
        //     }}
        //     className="border-none"
        //     icon={<EditPopupIcon />}
        //   />
        // ),
      },
      {
        key: 'billingStatus',
        label: t('ordersPage.orderPriceDataFields.billingStatus'),
        value: orderDetails?.billingStatus || 'N/A',
      },

      {
        key: 'totalDeclaredValue',
        label: t('ordersPage.orderPriceDataFields.totalDeclaredValue'),
        value: orderDetails?.declaredValue ? `$${orderDetails?.declaredValue}` : 'N/A',
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.totalDeclaredValue'),
                value: Number(orderDetails?.declaredValue) || 0,
                type: 'number',
                name: 'declaredValue',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'invoiceNumber',
        label: t('ordersPage.orderPriceDataFields.invoiceNumber'),
        value: orderDetails?.invoiceNumber || 'N/A',
      },

      {
        key: 'paymentStatus',
        label: t('ordersPage.orderPriceDataFields.paymentStatus'),
        value: orderDetails?.paymentStatus || 'N/A',
      },
    ],
  };
  const statisticInfoData = {
    Column: [
      {
        key: 'distance',
        label: `${t('ordersPage.statisticInfoDataFields.distanceKm')}(${orderDetails?.distanceUnit})`,
        value: orderDetails?.distance || 'N/A',
      },
      {
        key: 'totalQuantity',
        label: t('ordersPage.statisticInfoDataFields.totalQuantity'),
        value: orderDetails?.totalItems || 'N/A',
      },

      {
        key: 'combinedWeight',
        label: t('ordersPage.statisticInfoDataFields.combinedWeightKg'),
        value: orderDetails?.totalWeight || 'N/A',
      },
      {
        key: 'deliveryWaitTime',
        label: t('ordersPage.statisticInfoDataFields.deliveryWaitTime'),
        value: '00:00:00',
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.statisticInfoDataFields.deliveryWaitTime'),
                value: '00:00:00',
                type: 'time',
                name: 'deliveryWaitTime',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'pickupWaitTime',
        label: t('ordersPage.statisticInfoDataFields.pickupWaitTime'),
        value: '00:00:00',
        children: (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.statisticInfoDataFields.pickupWaitTime'),
                value: '00:00:00',
                type: 'time',
                name: 'pickupWaitTime',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
    ],
  };
  const customerInfoData = {
    Column: [
      {
        key: 'company',
        label: t('ordersPage.customerInfoDataFields.company'),
        value: orderDetails?.companyName || 'N/A',
      },
      {
        key: 'customer',
        label: t('ordersPage.customerInfoDataFields.customer'),
        value: orderDetails?.customerName || 'N/A',
      },
      {
        key: 'phone',
        label: t('ordersPage.customerInfoDataFields.phone'),
        value: orderDetails?.customerPhoneNumber || 'N/A',
      },
      {
        key: 'email',
        label: t('ordersPage.customerInfoDataFields.email'),
        value: orderDetails?.customerEmail || 'N/A',
      },
      {
        key: 'contact',
        label: t('ordersPage.customerInfoDataFields.contact'),
        value: orderDetails?.customerContactName || 'N/A',
      },
    ],
  };

  const PhoneNumberAddonBefore = useMemo(
    () => (
      <Form.Item name="countryCode" noStyle initialValue={optionsForPrefix[0].value}>
        <Select
          options={optionsForPrefix}
          placeholder="USA +1"
          onChange={(value) => maskingInputPhone(value)}
        />
      </Form.Item>
    ),
    [maskingInputPhone]
  );

  const renderFieldBasedOnType = (type: string, name: string, value: string) => {
    switch (type) {
      case 'number':
        return (
          <Form.Item name={name} initialValue={value}>
            <InputNumber
              inputMode="decimal"
              formatter={(value) => (value ? `$${value}` : '')}
              maxLength={10}
              className="bulk-adjust-input w-full"
              onKeyDown={(event) => numberFieldValidator(event, { allowDecimals: true })}
            />
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item className="w-full max-h-[200px]" name={name}>
            <DatePicker
              use12Hours
              showTime
              hourStep={1}
              defaultValue={value}
              format="DD/MM/YYYY hh:mm A"
              className="orders-general-datepicker w-full h-[40px]"
              suffixIcon={<DateCalendarIcon />}
              showNow={false}
              popupClassName="orders-general-datepicker-dropdown"
            />
          </Form.Item>
        );

      case 'dropdown':
        return (
          <Form.Item name={name} initialValue={value}>
            <Select
              suffixIcon={
                <span className="fields-dropdown-icon">
                  <CollapseUpIcon />
                </span>
              }
              placeholder="Select"
              options={isFieldModalOpen?.options}
              className="w-full h-[40px]"
            />
          </Form.Item>
        );
      case 'isCod':
        return (
          <div className="w-full flex gap-5 pb-10">
            <Form.Item
              layout="vertical"
              label={t('ordersPage.collectAt')}
              className="w-1/2 mb-0"
              name={name}
            >
              <Select
                className="h-[40px]"
                labelInValue
                suffixIcon={
                  <span className="fields-dropdown-icon">
                    <CollapseUpIcon />
                  </span>
                }
                placeholder="Select"
              />
            </Form.Item>
            <Form.Item
              layout="vertical"
              label={t('ordersPage.amountToCollect')}
              className="w-1/2 mb-0"
            >
              <InputNumber className="w-full h-[40px]" placeholder="$0.00" />
            </Form.Item>
          </div>
        );
      case 'textarea':
        return (
          <Form.Item name={name} initialValue={value}>
            <TextArea rows={4} />
          </Form.Item>
        );
      case 'text':
        return (
          <Form.Item name={name} initialValue={value}>
            <Input maxLength={255} />
          </Form.Item>
        );
      case 'time':
        return (
          <Form.Item name={name} initialValue={convertInUtcToDayjs(value)}>
            <TimePicker use12Hours className="w-full h-[40px]" format={'hh:mm:ss'} />
          </Form.Item>
        );
      case 'masked':
        return (
          <Form.Item name={name}>
            <MaskedInput defaultValue={value} mask={optionsForPrefix[0].mask} />
          </Form.Item>
        );
      case 'email':
        return (
          <Form.Item
            name={name}
            rules={[
              {
                required: true,
                message: 'Please enter email',
              },
              {
                type: 'email',
                message: t('addressPage.operationalForm.emailTypeError'),
              },
            ]}
          >
            <Input type="email" placeholder="Enter email" />
          </Form.Item>
        );
      default:
        return;
    }
  };
  const headerForOrderChange = () => (
    <div className="flex flex-col gap-2">
      <div className="flex gap-3">
        <OrdersWarningIcon />
        <span className="text-[16px] font-semibold">{t('ordersPage.orderDetailsChange')}</span>
      </div>
      <p>
        The order total can be updated from $161.53 to $199.42 due to pricing changes. Keep current
        prices or update to new ones?
      </p>
    </div>
  );

  const orderGeneralInformation = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {orderInfoData?.Column?.map((item, index) => {
          return (
            <div className="flex w-full p-1">
              <div key={index} className="flex items-center w-2/3">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
              {item.children && (
                <div className="w-1/3 px-2 flex justify-end items-center">{item.children}</div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  const orderPriceInfo = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {orderPriceData?.Column?.map((item, index) => {
          return (
            <div className="flex w-full p-1">
              <div key={index} className="flex items-center w-2/3">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
              {item.children && (
                <div className="w-1/3 px-2 flex justify-end items-center">{item.children}</div>
              )}
            </div>
          );
        })}
      </div>
    );
  };
  const orderStatisticInfo = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {statisticInfoData?.Column?.map((item, index) => {
          return (
            <div className="flex w-full p-1">
              <div key={index} className="flex items-center w-2/3">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
              {item.children && <div className="w-1/3 px-2 flex justify-end">{item.children}</div>}
            </div>
          );
        })}
      </div>
    );
  };
  const customerOrderInfo = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {customerInfoData?.Column?.map((item, index) => {
          return (
            <div className="flex w-full p-1">
              <div key={index} className="flex items-center w-2/3">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  const childForOrderChange = () => (
    <div>
      <ul className="list-disc flex  flex-col gap-2  ml-[70px]">
        <li>Fuel surcharge 15% changed from $14.36 to $19.94</li>
        <li>Hubscher 1st skid discount changes from ($47.18) to $0.00 in Aug 2024.</li>
        <li>Common final weight charges changed from $105.50 to $115.50</li>
        <li>Add 20% changed from $31.45 to $26.58</li>
      </ul>
      <div className="flex flex-row gap-[40px] px-5 py-3">
        <Button
          onClick={() => {
            setOpenPanels([]);
          }}
          className="hover:!bg-[#FFFAEB] p-0 bg-[#FFFAEB] border-none"
        >
          <span className="text-primary-500 font-semibold text-[14px]">Accept</span>
        </Button>
        <Button
          onClick={() => {
            setOpenPanels([]);
          }}
          className="hover:!bg-[#FFFAEB] p-0 bg-[#FFFAEB] border-none"
        >
          <span className="text-primary-500 font-semibold text-[14px]">Dismiss</span>
        </Button>
      </div>
    </div>
  );

  const onPlaceChanged = useCallback(async () => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);
      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
      };
      form.setFieldsValue(newFormValues);
      try {
        form.resetFields(['zone']);
        if (selectedPlaceData?.postalCode) {
          const trimmedPostalCode = selectedPlaceData?.postalCode.split(' ')[0];
          const response = await zoneService.getById(`postalCode/${trimmedPostalCode}`);
          form.setFieldValue('zone', response.name);
          await form.validateFields(['zone']);
        } else {
          form.setFields([
            {
              name: 'zone',
              errors: [t('addressPage.operationalForm.noPostalCodeFound')],
            },
          ]);
        }
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorStack = error?.response?.data as TrackedError;
          if (errorStack?.code === 406007) {
            form.resetFields(['zone']);
            form.setFields([
              {
                name: 'zone',
                errors: [
                  t('addressPage.operationalForm.zoneError', {
                    code: errorStack?.details?.postalCode.split(' ')[0],
                  }),
                ],
              },
            ]);
          }
        }
      }
    }
  }, [form, searchResult, t]);

  const itemForOrderChange = [
    {
      key: '1',
      label: headerForOrderChange(),
      children: <div className="bg-[#FFFAEB]">{childForOrderChange()}</div>,
    },
  ];
  const itemsForOrderDetails = [
    {
      key: 1,
      label: 'General information',
      children: orderGeneralInformation(),
    },
    {
      key: 2,
      label: 'Prices and billing information',
      children: orderPriceInfo(),
    },
    {
      key: 3,
      label: 'Static information',
      children: orderStatisticInfo(),
    },
    {
      key: 4,
      label: 'Customer information',
      children: customerOrderInfo(),
    },
  ];

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex w-full bg-[#F5F6FF]">
        <div className="flex justify-end gap-3 w-full">
          <Button
            onClick={() => setIsOpen({ ...isOpen, isOpenModal: false })}
            className="border-primary-200 bg-[#FFFFFF] text-[#090A1A] hover:!border-primary-200 hover:!bg-[#FFFFFF] hover:!text-[#090A1A] rounded-lg border-[1px]"
          >
            {t('common.cancel')}
          </Button>
          <Button
            form="edit-location-form"
            loading={
              updateStatusMutation.isPending ||
              assignOrderMutation.isPending ||
              updateOrderMutation.isPending
            }
            htmlType="submit"
            type="primary"
            className="bg-primary-500 text-white border-primary-500 rounded-lg hover:!border-primary-500 hover:!text-[white] hover:!bg-primary-500"
          >
            {t('common.update')}
          </Button>
        </div>
      </footer>
    ),
    [
      assignOrderMutation.isPending,
      isOpen,
      t,
      updateOrderMutation.isPending,
      updateStatusMutation.isPending,
    ]
  );

  const FooterForFieldsModal = useMemo(
    () => (
      <footer className="custom-modals-footer flex w-full bg-[#F5F6FF]">
        <div className="flex justify-end gap-3 w-full">
          <Button
            onClick={() => setIsFieldModalOpen({ ...isFieldModalOpen, isOpenModal: false })}
            className="border-primary-200 bg-[#FFFFFF] text-[#090A1A] hover:!border-primary-200 hover:!bg-[#FFFFFF] hover:!text-[#090A1A] rounded-lg border-[1px]"
          >
            {t('common.cancel')}
          </Button>
          <Button
            form="fieldsForm"
            loading={updateOrderMutation.isPending}
            htmlType="submit"
            type="primary"
            className="bg-primary-500 text-white border-primary-500 rounded-lg hover:!border-primary-500 hover:!text-[white] hover:!bg-primary-500"
          >
            {t('common.update')}
          </Button>
        </div>
      </footer>
    ),
    [isFieldModalOpen, t, updateOrderMutation.isPending]
  );

  const postalCodeWatcherValue = Form.useWatch('postalCode', form);
  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);

  const closeModalHandler = useCallback(() => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          setIsOpen({ isOpenModal: false, isOpenTitle: '', isOpenDescription: '' });
          setPreventExit(false);
          setIsBlocked(false);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
      return;
    }
    setIsOpen({ isOpenModal: false, isOpenTitle: '', isOpenDescription: '' });
  }, [isBlocked, setIsBlocked, setPreventExit, t]);

  return (
    <>
      <CustomModal
        className="!w-[500px]"
        style={{ top: '35%' }}
        maskClosable={false}
        destroyOnClose
        onCancel={() => {
          setIsFieldModalOpen({
            isOpenModal: false,
            isOpenTitle: '',
            value: '',
            type: '',
            fieldName: '',
            name: '',
            options: undefined,
            onFinish: undefined,
          });
        }}
        modalTitle={isFieldModalOpen?.isOpenTitle}
        modalDescription=""
        open={isFieldModalOpen?.isOpenModal}
        footer={FooterForFieldsModal}
      >
        <div className="flex flex-col gap-2">
          <span>{isFieldModalOpen?.fieldName ?? isFieldModalOpen?.isOpenTitle}</span>
          <Form
            className="custom-form"
            form={fieldsForm}
            name="fieldsForm"
            onFinish={(values) => updateOrderHandler({ payload: values, showNotification: true })}
            preserve={false}
          >
            {renderFieldBasedOnType(
              isFieldModalOpen?.type,
              isFieldModalOpen.name,
              isFieldModalOpen.value
            )}
          </Form>
        </div>
      </CustomModal>
      <CustomModal
        maskClosable={false}
        destroyOnClose
        onCancel={closeModalHandler}
        footer={Footer}
        open={isOpen.isOpenModal}
        modalTitle={isOpen.isOpenTitle}
        modalDescription={isOpen.isOpenDescription}
      >
        <Form
          name="edit-location-form"
          layout="vertical"
          className="custom-form"
          form={form}
          onFinishFailed={(errors) => {
            form.scrollToField(errors.errorFields[0].name, { behavior: 'smooth' });
          }}
          onFieldsChange={(changesFields) => {
            if (changesFields.length <= 1) {
              const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true), [
                'zone',
                'countryCode',
              ]);
              setPreventExit(isIsChange);
            } else if (changesFields.length > 1) {
              setIsBlocked(false);
              setPreventExit(false);
            }
          }}
          preserve={false}
        >
          <CustomDivider label={t('common.divider.basicDetails')} />
          <div className="form-fields-wrapper flex gap-2.5 flex-col">
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.name')}
                  </span>
                }
                validateFirst
                name="name"
                rules={[
                  { required: true, message: t('addressPage.operationalForm.nameError') },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
              >
                <Input
                  placeholder={t('addressPage.operationalForm.namePlaceholder')}
                  maxLength={255}
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.companyName')}
                  </span>
                }
                name="companyName"
                validateFirst
                rules={[
                  {
                    required: true,
                    message: t('addressPage.operationalForm.companyNameError'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
              >
                <Input
                  placeholder={t('addressPage.operationalForm.companyNamePlaceholder')}
                  maxLength={255}
                />
              </Form.Item>
            </div>

            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.email')}
                  </span>
                }
                name="email"
                rules={[
                  {
                    required: true,
                    message: t('addressPage.operationalForm.emailError'),
                  },
                  { type: 'email', message: t('addressPage.operationalForm.emailTypeError') },
                ]}
              >
                <Input
                  placeholder={t('addressPage.operationalForm.emailPlaceholder')}
                  maxLength={255}
                />
              </Form.Item>

              <Space.Compact className="combined-input">
                <Form.Item
                  dependencies={['countryCode']}
                  validateFirst
                  className="w-[75%]"
                  rules={[
                    {
                      required: true,
                      validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                    },
                    {
                      validator: (_, value) =>
                        validateMaskedInput(
                          value,
                          maskPhoneInput.length,
                          t('addressPage.operationalForm.validPhoneNumberError')
                        ),
                    },
                  ]}
                  name="phoneNumber"
                  label={
                    <span className="font-semibold text-[#20363F]">
                      {t('addressPage.operationalForm.phoneNumber')}
                    </span>
                  }
                >
                  <MaskedInput
                    ref={inputPhoneRef}
                    addonBefore={PhoneNumberAddonBefore}
                    className="customer-general-maskedInput address-popup-maskedInput"
                    placeholder={t('addressPage.operationalForm.phoneNumberPlaceholder')}
                    mask={maskPhoneInput.mask}
                    onChange={(event) => form.setFieldValue('phoneNumber', event?.unmaskedValue)}
                  />
                </Form.Item>
                <Form.Item name="phoneExtension" className="w-[25%]">
                  <Input
                    placeholder={t('addressPage.operationalForm.phoneExtPlaceholder')}
                    maxLength={6}
                    onKeyDown={(event) => numberFieldValidator(event, {})}
                  />
                </Form.Item>
              </Space.Compact>
            </div>
            <CustomDivider label={t('addressPage.operationalForm.locationDividerText')} />
            <CustomGoogleAutoComplete
              onLoad={onLoad}
              onPlaceChanged={onPlaceChanged}
              ref={autocompleteRef}
            >
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.addressLine1')}
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t('addressPage.operationalForm.addressLine1Error'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
                name="addressLine1"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.addressLine1Placeholder')}
                  maxLength={255}
                  id="autoComplete"
                />
              </Form.Item>
            </CustomGoogleAutoComplete>

            <Form.Item
              label={
                <span className="font-semibold text-[#20363F]">
                  {t('addressPage.operationalForm.addressLine2')}
                </span>
              }
              name="addressLine2"
              rules={[
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
              ]}
            >
              <Input
                placeholder={t('addressPage.operationalForm.addressLine2Placeholder')}
                maxLength={255}
              />
            </Form.Item>
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.city')}
                  </span>
                }
                rules={[{ required: true, message: t('addressPage.operationalForm.cityError') }]}
                name="city"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.cityPlaceholder')}
                  maxLength={255}
                  disabled
                />
              </Form.Item>

              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.province')}
                  </span>
                }
                rules={[
                  { required: true, message: t('addressPage.operationalForm.provinceError') },
                ]}
                name="province"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.provincePlaceholder')}
                  maxLength={100}
                  disabled
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.postalCode')}
                  </span>
                }
                validateFirst
                rules={[
                  { required: true, message: t('addressPage.operationalForm.postalCodeError') },
                  {
                    pattern: formErrorRegex.PostalCode,
                    message: t('addressPage.operationalForm.validPostalCodeError'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
                name="postalCode"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.postalCodePlaceholder')}
                  maxLength={20}
                  disabled
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.country')}
                  </span>
                }
                rules={[{ required: true, message: t('addressPage.operationalForm.countryError') }]}
                name="country"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.countryPlaceholder')}
                  maxLength={100}
                  disabled
                />
              </Form.Item>
            </div>

            <Form.Item
              label={
                <span className="flex gap-1 font-semibold text-[#20363F]">
                  {t('addressPage.operationalForm.zone')}
                  <CustomTooltip title={t('customerAddressPage.operationalForm.zoneToolTip')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </CustomTooltip>
                </span>
              }
              validateFirst
              rules={[
                {
                  required: true,
                  message: postalCodeWatcherValue
                    ? t('addressPage.operationalForm.zoneError', {
                        code: postalCodeWatcherValue.split(' ')[0],
                      })
                    : t('addressPage.operationalForm.noPostalCodeFound'),
                },
              ]}
              name="zone"
              dependencies={['addressLine1']}
            >
              <Input
                placeholder={`${t('addressPage.operationalForm.zonePlaceholder')}`}
                maxLength={50}
                disabled
              />
            </Form.Item>

            <CustomDivider label={t('addressPage.operationalForm.commentsDividerTex')} />

            <Form.Item
              label={
                <>
                  <CustomTooltip title={t('customerAddressPage.operationalForm.notesTooltip')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </CustomTooltip>
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.comments')}
                  </span>
                </>
              }
              name="notes"
            >
              <TextArea
                placeholder={t('addressPage.operationalForm.commentsPlaceHolder')}
                maxLength={255}
                style={{
                  minHeight: 54,
                  maxHeight: 100,
                }}
              />
            </Form.Item>
          </div>
        </Form>
      </CustomModal>
      <div className="py-5 pr-5 flex flex-col w-[100%] gap-5 max-h-[84.5vh] overflow-y-auto">
        <Collapse
          expandIcon={({ isActive }) => {
            return (
              <span className="text-primary-500 font-semibold text-[14px]">
                {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
              </span>
            );
          }}
          activeKey={openPanels}
          collapsible="header"
          onChange={(key) => {
            setOpenPanels(key as string[]);
          }}
          defaultActiveKey={'1'}
          className="order-changes-collapse bg-[#FFFAEB] !border-[1px] !border-[#F79009] rounded-[8px]"
          items={itemForOrderChange}
          expandIconPosition="end"
          accordion
        />
        <OrderStatusSteps orderDetails={orderDetails as any} />

        <CustomDivider label={t('ordersPage.orderDetails')} />
        <div className="flex w-full sm:flex-col lg:flex-row gap-3">
          <Card className="sm:w-full lg:w-1/2 bg-[#F5F6FF] border-none rounded-sm">
            <div className="flex flex-col gap-3">
              <div className="flex w-full justify-between ">
                <div className="flex gap-2 justify-center align-middle items-center">
                  <span className="text-[14px] font-semibold">
                    {t('ordersPage.collectionLocation')}
                  </span>
                  <span className="bg-[#12B76A] text-white px-2 py-[2px] rounded-md text-[12px]">
                    {orderDetails?.collectionZoneName || 'N/A'}{' '}
                  </span>
                </div>
                <Button
                  onClick={() => {
                    setIsOpen({
                      isOpenModal: true,
                      isOpenTitle: t('ordersPage.collectionLocation'),
                      isOpenDescription: t('ordersPage.enterCollectionLocationDetails'),
                    });
                  }}
                  icon={<EditPopupIcon />}
                  className="cursor-pointer bg-[#F5F6FF] border-none"
                />
              </div>
              <div className="flex flex-col gap-[3px]">
                <span className="text-[14px] font-semibold">
                  {orderDetails?.collectionCompanyName || 'N/A'}
                </span>
                <span className="text-[12px] font-semibold">
                  {orderDetails?.collectionContactName || 'N/A'}
                </span>
              </div>
              <div className="flex gap-2">
                <span className="w-[20px]">
                  <LocationOutlinedIcon />
                </span>
                <span className="text-[14px] font-semibold">
                  {orderDetails?.collectionAddress || 'N/A'}
                </span>
              </div>
            </div>
          </Card>
          <Card className="sm:w-full lg:w-1/2 bg-[#F5F6FF] border-none rounded-sm">
            <div className="flex flex-col gap-3">
              <div className="flex w-full justify-between ">
                <div className="flex gap-2 justify-center align-middle items-center">
                  <span className="text-[14px] font-semibold">
                    {t('ordersPage.deliveryLocation')}
                  </span>
                  <span className="bg-[#12B76A] text-white px-2 py-[2px] rounded-md text-[12px]">
                    {orderDetails?.deliveryZoneName || 'N/A'}
                  </span>
                </div>
                <Button
                  icon={<EditPopupIcon />}
                  onClick={() => {
                    setIsOpen({
                      isOpenModal: true,
                      isOpenTitle: t('ordersPage.deliveryLocation'),
                      isOpenDescription: t('ordersPage.enterDeliveryLocationDetails'),
                    });
                  }}
                  className="cursor-pointer bg-[#F5F6FF] border-none"
                />
              </div>
              <div className="flex flex-col gap-[3px]">
                <span className="text-[14px] font-semibold">
                  {orderDetails?.deliveryCompanyName || 'N/A'}
                </span>
                <span className="text-[12px] font-semibold">
                  {' '}
                  {orderDetails?.deliveryContactName}
                </span>
              </div>
              <div className="flex gap-2">
                <span className="w-[20px]">
                  <LocationOutlinedIcon />
                </span>
                <span className="text-[14px] font-semibold text-wrap">
                  {orderDetails?.deliveryAddress || 'N/A'}
                </span>
              </div>
            </div>
          </Card>
        </div>
        <Collapse
          expandIcon={({ isActive }) => {
            return (
              <span className="text-primary-500 font-semibold text-[14px]">
                {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
              </span>
            );
          }}
          defaultActiveKey={['1', '2', '3', '4']}
          collapsible="header"
          items={itemsForOrderDetails}
          className="order-details-collapse"
          bordered={false}
          expandIconPosition="end"
        ></Collapse>
      </div>
    </>
  );
};
export default OrdersGeneralComponent;
