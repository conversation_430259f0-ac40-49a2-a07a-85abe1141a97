import { CellClickedEvent, ICellRendererParams } from 'ag-grid-community';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  DeleteIcon,
  deleteSvg,
  DuplicateCustomerIcon,
  EyeIcon,
  HistoryIcon,
  PlusButtonIcon,
} from '@/assets';
import Icon from '@ant-design/icons/lib/components/Icon';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IContextMenuItems } from '@customTypes/ContextMenuTypes';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button, Divider, Form } from 'antd';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import ColumnManage from '@/components/specific/columnManage';
import AddressOperationForm from './addressOperation';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { GridNames } from '@/types/AppEvents';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes.ts';
import notificationManagerInstance, { useNotificationManager } from '@/hooks/useNotificationManger';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import usePreventExits from '@/hooks/usePreventExits';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { IIsOpenModal } from '@/types/CommonTypes';
import AddLocationIcon from '@/assets/icons/addLocationIcon';
import { addressServiceHook } from '@/api/address/useAddress';
import { CreateAddressDto, GetAddressDto } from '@/api/address/address.types';
import useThrottle from '@/hooks/useThrottle';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { zoneService } from '@/api/zones/useZones';
import { defaultPagination } from '@/constant/generalConstant';
import { getPaginationData } from '@/lib/helper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';

const AddressPage = () => {
  const [isModalOpen, setIsModalOpen] = useState<IIsOpenModal>({ isOpen: false, isEdit: false });
  const [form] = Form.useForm();
  const { t } = useLanguage();
  const [address, setAddress] = useState<GetAddressDto[]>();
  const gridRef = useRef<AgGridReact<GetAddressDto>>(null);
  const [cellData, setCellData] = useState<GetAddressDto>({} as GetAddressDto);
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const notificationManager = useNotificationManager();
  const { setPreventExit } = usePreventExits();
  const { isBlocked, setIsBlocked } = useNavigationContext();

  const {
    data: allAddresses,
    refetch: refetchAddresses,
    isLoading,
    isFetching,
  } = addressServiceHook.useList(filterParams);

  useEffect(() => {
    if (allAddresses) {
      setAddress(allAddresses.data || []);
    }
  }, [allAddresses]);

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.address.sortable.includes(field);
  }, []);

  const deleteMutation = addressServiceHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('addressPage.notification.successAddressDelete'),
      });
      await refetchAddresses();
    },
  });

  const createMutation = addressServiceHook.useCreate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('addressPage.notification.successAddressAdd'),
      });
      await refetchAddresses();
    },
  });

  const updateMutation = addressServiceHook.useUpdate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('addressPage.notification.successAddressUpdate'),
      });
      await refetchAddresses();
    },
  });

  const duplicateMutation = addressServiceHook.useDuplicate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('addressPage.notification.successAddressDuplicate'),
      });
      await refetchAddresses();
    },
    onError() {
      return notificationManager.error({
        message: t('common.error'),
        description: t('addressPage.notification.failedDuplicateUpdate'),
      });
    },
  });

  const handleDeleteAddress = useThrottle(async (id: string) => {
    await deleteMutation.mutateAsync(id);
    customAlert.destroy();
  }, 3000);

  const deleteAddressConfirmation = useCallback(
    (id: string) =>
      customAlert.error({
        title: t('addressPage.alert.deleteConfirmation'),
        message: t('addressPage.alert.deleteConfirmationMessage'),
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
        firstButtonLoading: deleteMutation.isPending,
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonFunction: async () => {
          await handleDeleteAddress(id);
        },
      }),
    [deleteMutation.isPending, handleDeleteAddress, t]
  );

  const onViewAddressHandler = useCallback(
    async (params: ICellRendererParams | CellClickedEvent) => {
      setIsModalOpen({ isOpen: true, isEdit: true });
      setCellData(params.data);

      const dataToSet = {
        ...params.data,
        customerId: params.data.customerId,
        addressLine1: params.data.addressLine1,
        addressLine2: params.data.addressLine2,
        city: params.data.city,
        notes: params.data.notes,
        companyName: params.data.companyName,
        country: params.data.country,
        customer: params.data.customer,
        email: params.data.email,
        name: params.data.name,
        phoneNumber: params.data.phoneNumber && String(params.data.phoneNumber),
        phoneExtension: params.data.phoneExtension && String(params.data.phoneExtension),
        countryCode: params.data.countryCode,
        postalCode: params.data.postalCode,
        province: params.data.province,
      };
      form.setFieldsValue(dataToSet);

      if (params.data?.postalCode) {
        const trimmedPostalCode = params?.data.postalCode.split(' ')[0];
        const response = await zoneService.getById(`postalCode/${trimmedPostalCode}`);
        form.setFieldValue('zone', response.name);
      }
    },
    [form]
  );

  const addressColDefs: IColDef[] = useMemo(
    () => [
      {
        field: 'customer',
        headerName: t('addressPage.colDefs.customer'),
        visible: true,
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('customer'),
        unSortIcon: isColumnSortable('customer'),
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'companyName',
        headerName: t('addressPage.colDefs.companyName'),
        visible: true,
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('companyName'),
        unSortIcon: isColumnSortable('companyName'),
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'name',
        headerName: t('addressPage.colDefs.name'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'addressLine1',
        headerName: t('addressPage.colDefs.addressLine1'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('addressLine1'),
        unSortIcon: isColumnSortable('addressLine1'),
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'addressLine2',
        headerName: t('addressPage.colDefs.addressLine2'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('addressLine2'),
        unSortIcon: isColumnSortable('addressLine2'),
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'city',
        headerName: t('addressPage.colDefs.city'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('city'),
        unSortIcon: isColumnSortable('city'),
        type: 'string',
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'postalCode',
        headerName: t('addressPage.colDefs.postalCode'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('postalCode'),
        unSortIcon: isColumnSortable('postalCode'),
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'phoneNumber',
        headerName: t('addressPage.colDefs.phone'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('phoneNumber'),
        unSortIcon: isColumnSortable('phoneNumber'),
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'email',
        headerName: t('addressPage.colDefs.email'),
        flex: 1,
        minWidth: 170,
        sortable: isColumnSortable('email'),
        unSortIcon: isColumnSortable('email'),
        type: 'string',
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'action',
        headerName: t('addressPage.colDefs.action'),
        pinned: 'right',
        width: 100,
        resizable: false,
        sortable: false,
        visible: true,
        cellRenderer: (params: ICellRendererParams<GetAddressDto>) => (
          <div className="flex gap-2 h-full items-center">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  {t('common.added')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params?.data?.createdAt as string)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.modified')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params?.data?.updatedAt as string)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.lastUpdatedBy')}:
                  <span className="block w-fit text-sm font-semibold">
                    {(params.data?.updatedBy as string) || t('common.notUpdatedYet')}
                  </span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>

            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() => onViewAddressHandler(params)}
            />

            <Icon
              component={DeleteIcon}
              onClick={() => deleteAddressConfirmation(params.data?.id as string)}
              className="cursor-pointer"
              alt="delete"
            />
          </div>
        ),
      },
    ],
    [t, isColumnSortable, searchText, onViewAddressHandler, deleteAddressConfirmation]
  );

  const duplicateAddressConfirmation = useCallback(
    (id: string) =>
      customAlert.warning({
        title: t('customerAddressPage.notifications.confirmDuplicate'),
        message: t('customerAddressPage.notifications.confirmDuplicateMessage'),
        secondButtonTitle: t('common.cancel'),
        firstButtonTitle: t('common.duplicate'),
        firstButtonLoading: duplicateMutation.isPending,
        secondButtonLoading: duplicateMutation.isPending,
        firstButtonFunction: async () => {
          if (!id) {
            notificationManager.error({
              message: t('common.error'),
              description: t('addressPage.notification.addressIdNotFound'),
            });
          }
          await duplicateMutation.mutateAsync(id as string);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      }),
    [duplicateMutation, notificationManager, t]
  );

  const customerContextMenuItems: IContextMenuItems[] = useMemo(
    () => [
      {
        label: t('addressPage.contextMenu.newAddress'),
        key: 'newAddress',
        icon: AddLocationIcon as React.ElementType,
        onClick: () => setIsModalOpen({ isOpen: true }),
      },
      {
        label: t('addressPage.contextMenu.duplicateAddress'),
        icon: DuplicateCustomerIcon as React.ElementType,
        onClick: () => duplicateAddressConfirmation(cellData.id as string),
        key: 'duplicateAddress',
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} />) as unknown as React.ElementType,
        onClick: () => deleteAddressConfirmation(cellData.id as string),
        key: 'deleteAddress',
      },
    ],
    [t, duplicateAddressConfirmation, deleteAddressConfirmation, cellData.id]
  );

  const closeModalHandler = useCallback(() => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          setIsModalOpen({ isOpen: false, isEdit: false });
          setPreventExit(false);
          setIsBlocked(false);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
      return;
    }
    setIsModalOpen({ isOpen: false, isEdit: false });
  }, [isBlocked, setIsBlocked, setPreventExit, t]);

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.addressGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="custom-antd-outlined" onClick={closeModalHandler}>
          {t('common.cancel')}
        </Button>
        <Button
          form="address-form"
          htmlType="submit"
          type="primary"
          loading={createMutation.isPending || updateMutation.isPending}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {isModalOpen.isEdit ? t('common.update') : t('common.save')}
        </Button>
      </footer>
    ),
    [closeModalHandler, createMutation.isPending, isModalOpen.isEdit, t, updateMutation.isPending]
  );

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  const onFinishHandler = useThrottle(async (values: CreateAddressDto) => {
    if (!values?.latitude || !values?.longitude) {
      notificationManagerInstance.error({
        message: t('common.error'),
        description: t('addressPage.operationalForm.noCoordinatesFound'),
      });
      return;
    }
    if (isModalOpen.isEdit) {
      if (!cellData.id) {
        return notificationManager.error({
          message: t('common.error'),
          description: t('addressPage.notification.addressIdNotFound'),
        });
      }
      delete values.customerId;
      await updateMutation.mutateAsync({ id: cellData.id as string, data: values });
    } else {
      await createMutation.mutateAsync(values);
    }
    setIsModalOpen({ isOpen: false, isEdit: false });
    setPreventExit(false);
  }, 3000);

  const paginationData = useMemo(() => getPaginationData(allAddresses), [allAddresses]);
  return (
    <>
      <CustomModal
        modalTitle={
          isModalOpen.isEdit
            ? t('addressPage.modal.editAddress')
            : t('addressPage.modal.addAddress')
        }
        modalDescription={
          isModalOpen.isEdit
            ? t('addressPage.modal.EditAddressDescription')
            : t('addressPage.modal.AddAddressDescription')
        }
        open={isModalOpen.isOpen}
        onCancel={closeModalHandler}
        footer={Footer}
        destroyOnClose
        maskClosable={false}
        keyboard={false}
      >
        <AddressOperationForm form={form} onFinish={onFinishHandler} open={isModalOpen} />
      </CustomModal>
      <div className="flex h-screen">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
            <div className="md:w-1/3 flex flex-col 3xsm:w-full">
              <PageHeadingComponent title={t('addressPage.header.title')} />
            </div>
            <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
              <div className="flex gap-3">
                <SearchFilterComponent
                  onSearch={triggerSearch}
                  colDefs={addressColDefs}
                  isSetQuickFilter={false}
                  searchInputPlaceholder={t('addressPage.header.searchAddress')}
                  onFilterApply={applyFilters}
                  setSelectedQuickFilterData={setSelectedQuickFilterData}
                  supportedFields={filterableModules.address.advanceFilter}
                  clearAllFunctionRef={clearAllFunctionRef}
                  setFilterParams={setFilterParams}
                />
                <ColumnManage colDefs={addressColDefs} gridName={GridNames.addressGrid} />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="h-[40px] !m-0" />
              </div>
              <div className="pt-5">
                <Button
                  className="h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                  onClick={() => setIsModalOpen({ isOpen: true })}
                >
                  {t('addressPage.header.addAddress')}
                </Button>
              </div>
            </div>
          </div>
          <main className="overflow-x-hidden overflow-y-auto bg-white">
            <ActiveFilters
              selectedQuickFilterData={selectedQuickFilterData}
              clearAllToDefault={clearAllToDefault}
              colDefs={addressColDefs}
            />
            <div className="mx-auto pr-6 py-5 flex justify-center items-center ">
              <CustomAgGrid
                className={selectedQuickFilterData.length > 0 ? '!h-[80vh]' : ''}
                gridRef={gridRef}
                loading={isLoading || isFetching}
                rowData={address}
                columnDefs={addressColDefs}
                isContextMenu
                contextMenuItem={customerContextMenuItems}
                onContextMenu={(params) => setCellData(params.data)}
                onCellClicked={(params) => {
                  if (params.colDef.field !== 'action') {
                    onViewAddressHandler(params);
                  }
                }}
                gridName={GridNames.addressGrid}
                paginationProps={{
                  ...paginationData,
                  onPaginationChange(page, pageLimit) {
                    setFilterParams((prev) => ({
                      ...prev,
                      pageNumber: page,
                      pageSize: pageLimit,
                    }));
                  },
                }}
                onSortChanged={(params: IExtendedSortChangedEvent) =>
                  setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                }
                emptyState={{
                  title:
                    searchText || selectedQuickFilterData.length > 0
                      ? t('common.noMatchesFound')
                      : t('addressPage.emptyState.title'),
                  description:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('addressPage.emptyState.description'),
                  link:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('addressPage.emptyState.link'),
                  onLinkAction: () => setIsModalOpen({ isOpen: true, isEdit: false }),
                }}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default AddressPage;
