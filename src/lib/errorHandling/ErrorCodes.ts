import notificationManagerInstance from '@/hooks/useNotificationManger';
import { LanguagePath, translator } from '@/i18n/languageLoader';
import { TrackedError } from '@/types/AxiosTypes';

export const errorCodes: Record<number, LanguagePath> = {
  // System Errors (10xxxx)
  100001: 'systemErrors.unknown',
  100101: 'systemErrors.validationError',
  100201: 'systemErrors.configurationError',

  // Auth Errors (20xxxx)
  200101: 'systemErrors.authErrors.authenticationRequired',
  200201: 'systemErrors.authErrors.insufficientPermissions',
  200102: 'systemErrors.authErrors.invalidCredentials',
  200301: 'systemErrors.authErrors.tokenExpired',
  200302: 'systemErrors.authErrors.tokenExpired',
  200303: 'systemErrors.authErrors.tokenExpired',
  401: 'systemErrors.authErrors.tokenExpired',

  // Data Errors (30xxxx)
  300101: 'systemErrors.dataErrors.resourceNotFound',
  300201: 'systemErrors.dataErrors.duplicateEntry',
  300301: 'systemErrors.dataErrors.invalidDataFormat',
  300401: 'systemErrors.dataErrors.dataConstraintViolation',

  // Business Errors (40xxxx)
  401001: 'systemErrors.businessErrors.tenant.notFound',
  401002: 'systemErrors.businessErrors.tenant.alreadyExists',
  401003: 'systemErrors.businessErrors.tenant.inactive',
  401004: 'systemErrors.businessErrors.tenant.deleted',
  401005: 'systemErrors.businessErrors.tenant.invalidStatus',
  401006: 'systemErrors.businessErrors.tenant.operationNotAllowed',
  401007: 'systemErrors.businessErrors.tenant.uniqueConstraintViolation',

  402001: 'systemErrors.businessErrors.user.notFound',
  402002: 'systemErrors.businessErrors.user.alreadyExists',
  402003: 'systemErrors.businessErrors.user.inactive',
  402004: 'systemErrors.businessErrors.user.deleted',
  402005: 'systemErrors.businessErrors.user.emailExists',
  402006: 'systemErrors.businessErrors.user.invalidStatus',
  402007: 'systemErrors.businessErrors.user.operationNotAllowed',
  402008: 'systemErrors.businessErrors.user.credentialsExpired',

  403001: 'vehiclePage.messages.vehicleNotFound',
  403002: 'vehiclePage.messages.licensePlateExists',
  403003: 'systemErrors.businessErrors.vehicle.inactive',
  403004: 'systemErrors.businessErrors.vehicle.deleted',
  403005: 'systemErrors.businessErrors.vehicle.invalidStatus',
  403006: 'systemErrors.businessErrors.vehicle.operationNotAllowed',
  403007: 'systemErrors.businessErrors.vehicle.maintenanceOverdue',

  404001: 'systemErrors.businessErrors.vehicleType.notFound',
  404002: 'systemErrors.businessErrors.vehicleType.alreadyExists',
  404003: 'systemErrors.businessErrors.vehicleType.inactive',
  404004: 'systemErrors.businessErrors.vehicleType.deleted',
  404005: 'systemErrors.businessErrors.vehicleType.invalidStatus',
  404006: 'systemErrors.businessErrors.vehicleType.operationNotAllowed',
  404007: 'systemErrors.businessErrors.vehicleType.inUse',

  //customer contacts
  403102: 'dashboard.customer.contactEmailExists',
  // address
  408004: 'addressPage.notification.cantDeleteDefaultAddress',
  408001: 'addressPage.notification.notFound',

  //time clocks session
  405006: 'vehiclePage.notificationMessages.sessionOverlaps',
  405007: 'vehiclePage.notificationMessages.sessionHourLimitExceed',

  // External Service Errors
  500001: 'systemErrors.externalServiceErrors.generic',
  500101: 'systemErrors.externalServiceErrors.httpRequestFailed',
  500201: 'systemErrors.externalServiceErrors.databaseError',
  500301: 'systemErrors.externalServiceErrors.fileUploadFailed',
  500302: 'systemErrors.externalServiceErrors.fileNotFound',
  500401: 'systemErrors.externalServiceErrors.emailSendFailed',

  // Infrastructure Errors
  600001: 'systemErrors.infrastructureErrors.generic',
  600101: 'systemErrors.infrastructureErrors.networkError',
  600201: 'systemErrors.infrastructureErrors.storageError',
  600301: 'systemErrors.infrastructureErrors.cacheError',
  600401: 'systemErrors.infrastructureErrors.queueError',

  //Price Modifiers Errors
  410002: 'systemErrors.businessErrors.priceModifiers.priceModifierExists',
  410003: 'systemErrors.businessErrors.priceModifiers.cannotDeletePriceModifier',
  410006: 'systemErrors.businessErrors.priceModifiers.groupModifierExists',
  410007: 'systemErrors.businessErrors.priceModifiers.cannotDeleteGroupModifier',

  //Zone
  406002: 'systemErrors.businessErrors.zone.zoneNameExists',

  //Zone Table
  407002: 'systemErrors.businessErrors.zoneTable.zoneTableExists',

  //Order
  // 412001: 'systemErrors.businessErrors.order.orderNotFound',
  412002: 'systemErrors.businessErrors.order.orderNotAssignedToDriver',
  412003: 'systemErrors.businessErrors.order.cannotTransitionFromDraftToCompleted',
  412004: 'systemErrors.businessErrors.order.orderUpdateFailed',
  412005: 'systemErrors.businessErrors.order.orderAlreadyAssigned',
  412006: 'systemErrors.businessErrors.order.orderDeliveryLocationNotFound',
  412007: 'systemErrors.businessErrors.order.orderPickupLocationNotFound',
  412008: 'systemErrors.businessErrors.order.orderCustomerSignatureRequired',
  412009: 'systemErrors.businessErrors.order.orderLocked',
  412010: 'systemErrors.businessErrors.order.orderCreationFailed',
  412011: 'systemErrors.businessErrors.order.orderItemNotFound',
  412012: 'systemErrors.businessErrors.order.orderCompletionRequirement',
  412013: 'systemErrors.businessErrors.order.orderDeliveryTimeInvalid',
} as const;

// Helper function to replace placeholders in the message
const replacePlaceholders = (message: string, values: Record<string, any>): string => {
  return message.replace(/{{(\w+)}}/g, (_, key) =>
    values[key] !== undefined ? values[key] : `{{${key}}}`
  );
};

export const handleSystemErrorNotification = (
  errorCode: keyof typeof errorCodes | string,
  errorStack: TrackedError
) => {
  if (!errorCodes[Number(errorCode)]) return;

  if (errorCode) {
    let message = translator(errorCodes[Number(errorCode)]);

    if (errorStack.details) {
      message = replacePlaceholders(message, errorStack.details);
    }

    if (errorCodes[Number(errorCode)]) {
      return notificationManagerInstance.error({
        message: translator('common.error'),
        description: message,
      });
    }
  } else {
    return notificationManagerInstance.error({
      message: translator('common.error'),
      description: translator('systemErrors.unknownException'),
    });
  }
};
